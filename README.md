# DCIM (Data Center Infrastructure Management) Addon Module for WHMCS

A comprehensive data center infrastructure management module for WHMCS that provides hosting providers and data center operators with complete physical infrastructure management capabilities integrated directly into the WHMCS admin interface.

## Features

### 🏢 Location Management
- **Hierarchical Structure**: Support for regions > facilities > floors > rooms
- **Complete CRUD Operations**: Create, read, update, delete locations
- **Capacity Tracking**: Total U-space, power, and cooling capacity
- **Contact Management**: Store contact information for each location
- **Status Management**: Active, inactive, maintenance states

### 🗄️ Rack Management
- **Visual U-Space Representation**: Interactive 42U rack visualization
- **Capacity Tracking**: Real-time occupancy and available space
- **Physical Specifications**: Height, power, cooling, weight limits
- **Position Mapping**: X/Y coordinates for floor plan integration
- **Reservation System**: Reserve racks for future use

### 🖥️ Server Management
- **Lifecycle Management**: Provisioning to decommissioning workflow
- **Hardware Tracking**: CPU, RAM, storage, serial numbers
- **Rack Assignment**: Precise U-space allocation with conflict detection
- **WHMCS Integration**: Link to WHMCS products and services
- **Status Tracking**: Provisioning, active, maintenance, failed states
- **Move History**: Audit trail of server relocations

### 🔌 Network Switch Management
- **Hierarchical Organization**: Location and rack-based organization
- **Port Management**: Track port count and connectivity
- **SNMP Integration**: Management IP and community strings
- **Stack Configuration**: Support for switch stacking
- **Cable Management**: Port assignment and connectivity mapping

### 🌐 IPAM (IP Address Management)
- **Subnet Hierarchy**: Parent/child subnet relationships
- **IPv4/IPv6 Support**: Dual-stack IP management
- **Automatic Allocation**: Intelligent IP assignment algorithms
- **WHMCS Integration**: Link IPs to clients and services
- **VLAN Association**: VLAN ID tracking and management
- **Bulk Operations**: Import/export and bulk assignment tools
- **DNS Integration**: Hostname and reverse DNS management

### 🔗 WHMCS Integration
- **Bidirectional Linking**: Connect physical assets to WHMCS entities
- **Automated Provisioning**: Hook-based automation for service lifecycle
- **Client Association**: Link IP addresses and servers to clients
- **Service Integration**: Connect servers to hosting products
- **Custom Fields**: Additional metadata storage via WHMCS custom fields

### 🔌 API Endpoints
- **RESTful Architecture**: Standard HTTP methods and status codes
- **Authentication**: API key-based authentication system
- **Comprehensive Coverage**: All CRUD operations via API
- **Rate Limiting**: Prevent API abuse with rate limiting
- **Documentation**: OpenAPI/Swagger documentation

### 🔒 Security and Permissions
- **Role-Based Access Control**: Granular permission matrix
- **Audit Logging**: Complete audit trail of all changes
- **Input Validation**: Comprehensive validation and sanitization
- **SQL Injection Prevention**: Prepared statements throughout
- **XSS Protection**: Output encoding and CSP headers

## Requirements

### System Requirements
- **WHMCS**: Version 8.0 or higher
- **PHP**: Version 7.4 or higher (8.0+ recommended)
- **Database**: MySQL 5.7+ or MariaDB 10.2+
- **Web Server**: Apache 2.4+ or Nginx 1.16+

### PHP Extensions
- PDO (with MySQL driver)
- JSON
- cURL
- OpenSSL
- mbstring

### Browser Support
- Chrome 70+
- Firefox 65+
- Safari 12+
- Edge 79+

## Installation

### 1. Download and Extract
1. Download the DCIM module package
2. Extract the contents to your WHMCS installation directory
3. Ensure the module files are located at: `modules/addons/dcim/`

### 2. File Permissions
Set appropriate file permissions:
```bash
chmod 755 modules/addons/dcim/
chmod 644 modules/addons/dcim/*.php
chmod 644 modules/addons/dcim/whmcs.json
chmod -R 644 modules/addons/dcim/lib/
chmod -R 644 modules/addons/dcim/templates/
chmod -R 644 modules/addons/dcim/assets/
chmod -R 644 modules/addons/dcim/lang/
```

### 3. Activate the Module
1. Log in to your WHMCS admin area
2. Navigate to **Configuration** > **System Settings** > **Addon Modules**
3. Find "DCIM - Data Center Infrastructure Management" in the list
4. Click **Activate**
5. Configure the module settings as needed
6. Set access permissions for administrator roles

### 4. Database Setup
The module will automatically create the required database tables upon activation. The following tables will be created:
- `dcim_locations`
- `dcim_racks`
- `dcim_servers`
- `dcim_switches`
- `dcim_ip_subnets`
- `dcim_ip_addresses`
- `dcim_audit_log`
- `dcim_permissions`

### 5. Initial Configuration
1. Access the module from **Addons** > **DCIM** in the admin menu
2. Create your first location (e.g., your data center facility)
3. Add racks to the location
4. Configure IP subnets for your network ranges
5. Set up administrator permissions as needed

## Configuration Options

The module provides several configuration options:

- **Enable Audit Logging**: Track all changes for compliance
- **Default Rack Height**: Set default U height for new racks (default: 42U)
- **Enable IP Auto Assignment**: Automatically assign IPs during provisioning
- **Enable API Access**: Allow external systems to access the API
- **API Rate Limit**: Control API usage (default: 1000 requests/hour)
- **Enable WHMCS Integration**: Link with WHMCS products and services
- **Notification Email**: Receive system notifications
- **Enable Visual Rack View**: Interactive rack visualization
- **Backup Retention**: Days to retain audit log backups (default: 30)

## Usage

### Getting Started
1. **Create Locations**: Start by creating your data center locations
2. **Add Racks**: Add server racks to your locations
3. **Configure IP Subnets**: Set up your network IP ranges
4. **Add Servers**: Register your physical servers
5. **Assign IP Addresses**: Allocate IPs to servers and services

### Location Hierarchy
The module supports a four-level hierarchy:
- **Region**: Geographic regions (e.g., "North America")
- **Facility**: Data center facilities (e.g., "NYC-DC1")
- **Floor**: Building floors (e.g., "Floor 3")
- **Room**: Individual rooms (e.g., "Server Room A")

### Rack Management
- Visual representation of 42U racks
- Drag-and-drop server placement
- Real-time capacity monitoring
- Conflict detection for overlapping assignments

### IP Management
- Hierarchical subnet organization
- Automatic IP allocation
- VLAN association
- Integration with WHMCS clients and services

## API Documentation

The module provides a comprehensive RESTful API for external integrations. API documentation is available at:
`/modules/addons/dcim/docs/api-documentation.md`

### Authentication
API requests require authentication using API keys. Contact your administrator to obtain API access.

### Rate Limiting
API requests are limited to prevent abuse. Default limit is 1000 requests per hour per IP address.

## Troubleshooting

### Common Issues

**Module not appearing in addon list**
- Verify file permissions are correct
- Check that all required files are present
- Ensure WHMCS version compatibility

**Database errors during activation**
- Verify MySQL/MariaDB version compatibility
- Check database user permissions
- Review error logs for specific issues

**Permission denied errors**
- Configure administrator role permissions
- Verify user has appropriate access levels
- Check audit log for access attempts

### Support

For technical support and assistance:
- **Email**: <EMAIL>
- **Documentation**: https://your-company.com/dcim/docs
- **Forum**: https://your-company.com/forum

## License

This module is licensed under the MIT License. See the LICENSE file for details.

## Changelog

### Version 1.0.0 (2025-01-17)
- Initial release
- Location management with hierarchical structure
- Rack management with visual representation
- Server management with WHMCS integration
- Switch management with port tracking
- IP address management (IPAM) with subnet hierarchy
- RESTful API endpoints
- Role-based permissions
- Audit logging system
- Responsive admin interface

## Contributing

We welcome contributions to improve the DCIM module. Please follow our contribution guidelines and submit pull requests for review.

## Credits

Developed by Your Company Name
- Lead Developer: Your Name
- UI/UX Design: Designer Name
- Quality Assurance: QA Team

---

For more information, visit our website at https://your-company.com
