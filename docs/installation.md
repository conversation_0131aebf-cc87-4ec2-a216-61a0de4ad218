# DCIM Module Installation Guide

This guide provides detailed instructions for installing and configuring the DCIM (Data Center Infrastructure Management) addon module for WHMCS.

## Prerequisites

Before installing the DCIM module, ensure your system meets the following requirements:

### System Requirements
- **WHMCS**: Version 8.0 or higher
- **PHP**: Version 7.4 or higher (PHP 8.0+ recommended)
- **Database**: MySQL 5.7+ or MariaDB 10.2+
- **Web Server**: Apache 2.4+ or Nginx 1.16+
- **Memory**: Minimum 256MB PHP memory limit (512MB recommended)
- **Disk Space**: At least 50MB free space

### Required PHP Extensions
Verify the following PHP extensions are installed and enabled:
- `pdo` (with MySQL driver)
- `json`
- `curl`
- `openssl`
- `mbstring`
- `zip` (for backup functionality)

### Database Permissions
The WHMCS database user must have the following permissions:
- `SELECT`
- `INSERT`
- `UPDATE`
- `DELETE`
- `CREATE` (for table creation)
- `ALTER` (for schema updates)
- `INDEX` (for index management)

## Installation Steps

### Step 1: Download and Extract

1. **Download the Module**
   - Download the DCIM module package from the official source
   - Verify the package integrity if checksums are provided

2. **Extract Files**
   ```bash
   # Extract to temporary directory
   unzip dcim-module-v1.0.0.zip -d /tmp/dcim-extract/
   
   # Copy to WHMCS installation
   cp -r /tmp/dcim-extract/modules/addons/dcim /path/to/whmcs/modules/addons/
   ```

3. **Verify File Structure**
   Ensure the following directory structure exists:
   ```
   /path/to/whmcs/modules/addons/dcim/
   ├── dcim.php
   ├── whmcs.json
   ├── lib/
   ├── templates/
   ├── assets/
   ├── lang/
   └── docs/
   ```

### Step 2: Set File Permissions

Set appropriate file permissions for security:

```bash
# Navigate to WHMCS directory
cd /path/to/whmcs/

# Set directory permissions
chmod 755 modules/addons/dcim/
find modules/addons/dcim/ -type d -exec chmod 755 {} \;

# Set file permissions
find modules/addons/dcim/ -type f -exec chmod 644 {} \;

# Ensure main module file is readable
chmod 644 modules/addons/dcim/dcim.php
```

### Step 3: Activate the Module

1. **Access WHMCS Admin Area**
   - Log in to your WHMCS admin panel
   - Navigate to **Configuration** → **System Settings** → **Addon Modules**

2. **Locate the Module**
   - Find "DCIM - Data Center Infrastructure Management" in the module list
   - If the module doesn't appear, verify file permissions and refresh the page

3. **Activate the Module**
   - Click the **Activate** button next to the DCIM module
   - The system will automatically create required database tables

4. **Configure Module Settings**
   - Click **Configure** to access module settings
   - Adjust configuration options as needed:
     - Enable Audit Logging: Recommended for compliance
     - Default Rack Height: Set to your standard (usually 42U)
     - Enable IP Auto Assignment: Enable for automated workflows
     - API Access: Enable if you need external integrations
     - WHMCS Integration: Enable for service linking

### Step 4: Set Access Permissions

1. **Configure Administrator Roles**
   - In the module configuration, scroll to **Access Control**
   - Select which administrator roles can access the module
   - For initial setup, grant access to "Full Administrator"

2. **Set Granular Permissions** (Optional)
   - After activation, access **Addons** → **DCIM** → **Permissions**
   - Configure specific permissions for each administrator role
   - Available permissions include:
     - View/Create/Edit/Delete for each module component
     - API access permissions
     - Report viewing permissions

### Step 5: Verify Installation

1. **Check Database Tables**
   Verify the following tables were created:
   ```sql
   SHOW TABLES LIKE 'dcim_%';
   ```
   
   Expected tables:
   - `dcim_locations`
   - `dcim_racks`
   - `dcim_servers`
   - `dcim_switches`
   - `dcim_ip_subnets`
   - `dcim_ip_addresses`
   - `dcim_audit_log`
   - `dcim_permissions`

2. **Access the Module**
   - Navigate to **Addons** → **DCIM** in the admin menu
   - You should see the DCIM dashboard
   - Verify all menu items are accessible based on your permissions

3. **Test Basic Functionality**
   - Create a test location
   - Add a test rack to the location
   - Verify the audit log is recording actions (if enabled)

## Post-Installation Configuration

### Initial Data Setup

1. **Create Location Hierarchy**
   ```
   Example structure:
   └── North America (Region)
       └── NYC-DC1 (Facility)
           ├── Floor 1 (Floor)
           │   ├── Server Room A (Room)
           │   └── Server Room B (Room)
           └── Floor 2 (Floor)
               └── Network Room (Room)
   ```

2. **Configure IP Subnets**
   - Set up your primary network ranges
   - Configure VLAN associations
   - Define gateway addresses

3. **Add Initial Racks**
   - Create racks in your server rooms
   - Set appropriate power and cooling capacities
   - Configure rack positions if using floor plans

### Integration with WHMCS

1. **Link Products to Servers**
   - Create dedicated server products in WHMCS
   - Use custom fields to store DCIM server IDs
   - Configure automated provisioning hooks

2. **IP Address Integration**
   - Link IP addresses to client services
   - Set up automatic IP assignment for new orders
   - Configure IP release on service termination

### Security Configuration

1. **Review Permissions**
   - Ensure only authorized staff have access
   - Configure role-based permissions appropriately
   - Enable audit logging for compliance

2. **API Security** (if enabled)
   - Generate secure API keys
   - Configure rate limiting
   - Restrict API access by IP if possible

## Troubleshooting

### Common Installation Issues

**Module not appearing in addon list**
- Verify file permissions: `chmod 644 modules/addons/dcim/dcim.php`
- Check PHP error logs for syntax errors
- Ensure all required files are present

**Database creation errors**
- Verify database user has CREATE permissions
- Check MySQL/MariaDB version compatibility
- Review WHMCS error logs for specific errors

**Permission denied when accessing module**
- Configure administrator role access in module settings
- Verify user is assigned to appropriate role
- Check if custom permissions are properly configured

**CSS/JavaScript not loading**
- Verify asset file permissions
- Check web server configuration for static file serving
- Clear browser cache and WHMCS template cache

### Error Log Locations

Check the following logs for troubleshooting:
- WHMCS Activity Log: **Utilities** → **Logs** → **Activity Log**
- PHP Error Log: Usually `/var/log/php/error.log` or as configured
- Web Server Error Log: `/var/log/apache2/error.log` or `/var/log/nginx/error.log`
- WHMCS Debug Log: Enable in **Configuration** → **System Settings** → **General Settings**

### Getting Help

If you encounter issues during installation:

1. **Check Documentation**
   - Review this installation guide
   - Consult the user manual
   - Check the API documentation

2. **Contact Support**
   - Email: <EMAIL>
   - Include WHMCS version, PHP version, and error messages
   - Provide relevant log entries

3. **Community Resources**
   - Visit our support forum
   - Check the knowledge base
   - Review frequently asked questions

## Upgrade Instructions

When upgrading to a newer version:

1. **Backup Current Installation**
   ```bash
   # Backup module files
   tar -czf dcim-backup-$(date +%Y%m%d).tar.gz modules/addons/dcim/
   
   # Backup database tables
   mysqldump -u username -p database_name dcim_* > dcim-tables-backup.sql
   ```

2. **Download New Version**
   - Download the latest module package
   - Review changelog for breaking changes

3. **Install Update**
   - Extract new files over existing installation
   - Access **Addon Modules** and click **Configure**
   - The module will automatically run database migrations

4. **Verify Upgrade**
   - Check module version in admin area
   - Test critical functionality
   - Review audit logs for any issues

## Uninstallation

To completely remove the DCIM module:

1. **Deactivate Module**
   - Go to **Configuration** → **System Settings** → **Addon Modules**
   - Click **Deactivate** next to the DCIM module

2. **Remove Files**
   ```bash
   rm -rf /path/to/whmcs/modules/addons/dcim/
   ```

3. **Remove Database Tables** (Optional)
   ```sql
   DROP TABLE IF EXISTS dcim_permissions;
   DROP TABLE IF EXISTS dcim_audit_log;
   DROP TABLE IF EXISTS dcim_ip_addresses;
   DROP TABLE IF EXISTS dcim_ip_subnets;
   DROP TABLE IF EXISTS dcim_switches;
   DROP TABLE IF EXISTS dcim_servers;
   DROP TABLE IF EXISTS dcim_racks;
   DROP TABLE IF EXISTS dcim_locations;
   ```

   **Warning**: This will permanently delete all DCIM data!

---

For additional support, please contact our technical team or visit our documentation portal.
