# DCIM Module API Documentation

The DCIM module provides a comprehensive RESTful API for external integrations and automation. This documentation covers all available endpoints, authentication methods, and usage examples.

## Table of Contents

1. [Authentication](#authentication)
2. [Rate Limiting](#rate-limiting)
3. [Response Format](#response-format)
4. [Error Handling](#error-handling)
5. [Locations API](#locations-api)
6. [Racks API](#racks-api)
7. [Servers API](#servers-api)
8. [Switches API](#switches-api)
9. [IP Management API](#ip-management-api)
10. [Statistics API](#statistics-api)
11. [Code Examples](#code-examples)

## Authentication

The DCIM API uses API key authentication. All requests must include a valid API key.

### Obtaining an API Key

1. Log in to WHMCS admin area
2. Navigate to **Configuration** → **System Settings** → **API Credentials**
3. Create a new API credential with DCIM access
4. Note the generated API identifier and secret

### Authentication Methods

#### Header Authentication (Recommended)
```http
Authorization: Bearer YOUR_API_KEY
Content-Type: application/json
```

#### Query Parameter Authentication
```http
GET /modules/addons/dcim/api/locations?api_key=YOUR_API_KEY
```

### API Base URL
```
https://your-whmcs-domain.com/modules/addons/dcim/api/
```

## Rate Limiting

API requests are limited to prevent abuse:

- **Default Limit**: 1000 requests per hour per IP address
- **Rate Limit Headers**: Included in all responses
  - `X-RateLimit-Limit`: Maximum requests per hour
  - `X-RateLimit-Remaining`: Remaining requests in current window
  - `X-RateLimit-Reset`: Unix timestamp when limit resets

### Rate Limit Exceeded Response
```json
{
  "success": false,
  "error": "Rate limit exceeded",
  "code": 429,
  "retry_after": 3600
}
```

## Response Format

All API responses follow a consistent JSON format:

### Success Response
```json
{
  "success": true,
  "data": {
    // Response data
  },
  "meta": {
    "total": 100,
    "page": 1,
    "per_page": 25,
    "total_pages": 4
  }
}
```

### Error Response
```json
{
  "success": false,
  "error": "Error message",
  "code": 400,
  "details": {
    "field": "Specific field error"
  }
}
```

## Error Handling

### HTTP Status Codes

- `200 OK`: Request successful
- `201 Created`: Resource created successfully
- `400 Bad Request`: Invalid request parameters
- `401 Unauthorized`: Invalid or missing API key
- `403 Forbidden`: Insufficient permissions
- `404 Not Found`: Resource not found
- `422 Unprocessable Entity`: Validation errors
- `429 Too Many Requests`: Rate limit exceeded
- `500 Internal Server Error`: Server error

### Common Error Codes

| Code | Description |
|------|-------------|
| `INVALID_API_KEY` | API key is invalid or expired |
| `INSUFFICIENT_PERMISSIONS` | User lacks required permissions |
| `VALIDATION_ERROR` | Request data validation failed |
| `RESOURCE_NOT_FOUND` | Requested resource doesn't exist |
| `DUPLICATE_ENTRY` | Resource already exists |
| `DEPENDENCY_ERROR` | Cannot delete due to dependencies |

## Locations API

### List Locations
```http
GET /api/locations
```

#### Parameters
- `page` (integer): Page number (default: 1)
- `limit` (integer): Items per page (default: 25, max: 100)
- `type` (string): Filter by location type (region, facility, floor, room)
- `status` (string): Filter by status (active, inactive, maintenance)
- `search` (string): Search in name, city, or address
- `parent_id` (integer): Filter by parent location

#### Response
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "NYC Data Center",
      "type": "facility",
      "parent_id": null,
      "parent_name": null,
      "address": "123 Data Center Drive",
      "city": "New York",
      "state": "NY",
      "country": "USA",
      "status": "active",
      "total_u_capacity": 1000,
      "power_capacity_watts": 100000,
      "created_at": "2025-01-17T10:00:00Z",
      "updated_at": "2025-01-17T10:00:00Z"
    }
  ],
  "meta": {
    "total": 1,
    "page": 1,
    "per_page": 25,
    "total_pages": 1
  }
}
```

### Get Location
```http
GET /api/locations/{id}
```

### Create Location
```http
POST /api/locations
```

#### Request Body
```json
{
  "name": "New Data Center",
  "type": "facility",
  "parent_id": null,
  "address": "456 Server Street",
  "city": "Boston",
  "state": "MA",
  "country": "USA",
  "status": "active",
  "total_u_capacity": 500,
  "power_capacity_watts": 50000,
  "contact_name": "John Doe",
  "contact_email": "<EMAIL>"
}
```

### Update Location
```http
PUT /api/locations/{id}
```

### Delete Location
```http
DELETE /api/locations/{id}
```

## Racks API

### List Racks
```http
GET /api/racks
```

#### Parameters
- `location_id` (integer): Filter by location
- `status` (string): Filter by status
- `search` (string): Search in name or description

### Get Rack
```http
GET /api/racks/{id}
```

#### Response includes occupancy information
```json
{
  "success": true,
  "data": {
    "id": 1,
    "name": "Rack-001",
    "location_id": 1,
    "location_name": "Server Room A",
    "height_u": 42,
    "power_capacity_watts": 5000,
    "status": "active",
    "occupancy": {
      "total_u": 42,
      "used_u": 15,
      "available_u": 27,
      "utilization_percent": 35.7
    },
    "servers": [
      {
        "id": 1,
        "hostname": "web01.example.com",
        "u_position": 1,
        "u_size": 1,
        "status": "active"
      }
    ]
  }
}
```

### Create Rack
```http
POST /api/racks
```

### Update Rack
```http
PUT /api/racks/{id}
```

### Delete Rack
```http
DELETE /api/racks/{id}
```

## Servers API

### List Servers
```http
GET /api/servers
```

#### Parameters
- `rack_id` (integer): Filter by rack
- `location_id` (integer): Filter by location
- `status` (string): Filter by status
- `whmcs_service_id` (integer): Filter by WHMCS service

### Get Server
```http
GET /api/servers/{id}
```

### Create Server
```http
POST /api/servers
```

#### Request Body
```json
{
  "hostname": "web02.example.com",
  "rack_id": 1,
  "u_position": 5,
  "u_size": 1,
  "manufacturer": "Dell",
  "model": "PowerEdge R640",
  "serial_number": "ABC123456",
  "cpu_specs": "2x Intel Xeon Gold 6248R",
  "ram_gb": 64,
  "storage_specs": "2x 480GB SSD RAID1",
  "power_consumption_watts": 350,
  "management_ip": "*************",
  "primary_ip": "********",
  "status": "provisioning",
  "whmcs_service_id": 123
}
```

### Update Server
```http
PUT /api/servers/{id}
```

### Delete Server
```http
DELETE /api/servers/{id}
```

### Move Server
```http
POST /api/servers/{id}/move
```

#### Request Body
```json
{
  "rack_id": 2,
  "u_position": 10
}
```

## Switches API

### List Switches
```http
GET /api/switches
```

### Get Switch
```http
GET /api/switches/{id}
```

### Create Switch
```http
POST /api/switches
```

### Update Switch
```http
PUT /api/switches/{id}
```

### Delete Switch
```http
DELETE /api/switches/{id}
```

## IP Management API

### List Subnets
```http
GET /api/subnets
```

### Get Subnet
```http
GET /api/subnets/{id}
```

#### Response includes IP utilization
```json
{
  "success": true,
  "data": {
    "id": 1,
    "name": "Main Network",
    "network": "***********",
    "prefix_length": 24,
    "ip_version": "4",
    "gateway": "***********",
    "vlan_id": 100,
    "status": "active",
    "utilization": {
      "total_ips": 254,
      "available_ips": 200,
      "assigned_ips": 45,
      "reserved_ips": 9,
      "utilization_percent": 21.3
    }
  }
}
```

### Create Subnet
```http
POST /api/subnets
```

### List IP Addresses
```http
GET /api/ip-addresses
```

#### Parameters
- `subnet_id` (integer): Filter by subnet
- `status` (string): Filter by status
- `assigned_to_type` (string): Filter by assignment type

### Assign IP Address
```http
POST /api/ip-addresses/{ip}/assign
```

#### Request Body
```json
{
  "assigned_to_type": "server",
  "assigned_to_id": 1,
  "hostname": "web01.example.com",
  "description": "Web server primary IP"
}
```

### Release IP Address
```http
POST /api/ip-addresses/{ip}/release
```

## Statistics API

### Get System Statistics
```http
GET /api/statistics
```

#### Response
```json
{
  "success": true,
  "data": {
    "locations": {
      "total": 5,
      "active": 4,
      "maintenance": 1
    },
    "racks": {
      "total": 25,
      "active": 23,
      "utilization_percent": 67.5
    },
    "servers": {
      "total": 150,
      "active": 142,
      "provisioning": 5,
      "maintenance": 3
    },
    "ip_addresses": {
      "total": 2048,
      "assigned": 1234,
      "available": 800,
      "utilization_percent": 60.3
    }
  }
}
```

### Get Location Statistics
```http
GET /api/locations/{id}/statistics
```

### Get Rack Statistics
```http
GET /api/racks/{id}/statistics
```

## Code Examples

### PHP Example
```php
<?php
$apiKey = 'your-api-key';
$baseUrl = 'https://your-whmcs-domain.com/modules/addons/dcim/api';

// Get all locations
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $baseUrl . '/locations');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Authorization: Bearer ' . $apiKey,
    'Content-Type: application/json'
]);

$response = curl_exec($ch);
$data = json_decode($response, true);

if ($data['success']) {
    foreach ($data['data'] as $location) {
        echo "Location: " . $location['name'] . "\n";
    }
}
curl_close($ch);
?>
```

### Python Example
```python
import requests
import json

api_key = 'your-api-key'
base_url = 'https://your-whmcs-domain.com/modules/addons/dcim/api'

headers = {
    'Authorization': f'Bearer {api_key}',
    'Content-Type': 'application/json'
}

# Create a new server
server_data = {
    'hostname': 'api-server-01.example.com',
    'rack_id': 1,
    'u_position': 15,
    'u_size': 1,
    'manufacturer': 'HP',
    'model': 'ProLiant DL360',
    'status': 'provisioning'
}

response = requests.post(
    f'{base_url}/servers',
    headers=headers,
    json=server_data
)

if response.status_code == 201:
    result = response.json()
    print(f"Server created with ID: {result['data']['id']}")
else:
    print(f"Error: {response.json()['error']}")
```

### JavaScript Example
```javascript
const apiKey = 'your-api-key';
const baseUrl = 'https://your-whmcs-domain.com/modules/addons/dcim/api';

// Get rack occupancy
async function getRackOccupancy(rackId) {
    try {
        const response = await fetch(`${baseUrl}/racks/${rackId}`, {
            headers: {
                'Authorization': `Bearer ${apiKey}`,
                'Content-Type': 'application/json'
            }
        });
        
        const data = await response.json();
        
        if (data.success) {
            console.log('Rack Occupancy:', data.data.occupancy);
            return data.data.occupancy;
        } else {
            console.error('Error:', data.error);
        }
    } catch (error) {
        console.error('Request failed:', error);
    }
}

getRackOccupancy(1);
```

## Webhooks

The DCIM module can send webhooks for important events:

### Webhook Events
- `server.created`: New server added
- `server.updated`: Server information changed
- `server.deleted`: Server removed
- `ip.assigned`: IP address assigned
- `ip.released`: IP address released
- `rack.full`: Rack capacity reached

### Webhook Configuration
Configure webhook URLs in the module settings to receive real-time notifications of infrastructure changes.

---

For additional API support or custom integrations, please contact our technical team.
