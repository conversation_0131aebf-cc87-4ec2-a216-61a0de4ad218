# DCIM Module User Manual

This comprehensive user manual provides detailed instructions for using the DCIM (Data Center Infrastructure Management) addon module for WHMCS.

## Table of Contents

1. [Getting Started](#getting-started)
2. [Dashboard Overview](#dashboard-overview)
3. [Location Management](#location-management)
4. [Rack Management](#rack-management)
5. [Server Management](#server-management)
6. [Switch Management](#switch-management)
7. [IP Address Management (IPAM)](#ip-address-management-ipam)
8. [WHMCS Integration](#whmcs-integration)
9. [Reports and Analytics](#reports-and-analytics)
10. [Permissions and Security](#permissions-and-security)
11. [Best Practices](#best-practices)
12. [Troubleshooting](#troubleshooting)

## Getting Started

### Accessing the DCIM Module

1. Log in to your WHMCS admin area
2. Navigate to **Addons** → **DCIM** in the main menu
3. You'll be taken to the DCIM dashboard

### First-Time Setup

When accessing the module for the first time, follow these steps:

1. **Create Your First Location**
   - Click on "Locations" from the dashboard
   - Add your primary data center facility
   - Configure address and contact information

2. **Set Up IP Subnets**
   - Navigate to "IP Management"
   - Create your primary network subnets
   - Configure VLAN associations

3. **Add Racks**
   - Go to "Racks" section
   - Add server racks to your location
   - Configure capacity and specifications

## Dashboard Overview

The DCIM dashboard provides a centralized view of your infrastructure:

### Quick Actions Panel
- **Locations**: Manage data center locations
- **Racks**: View and manage server racks
- **Servers**: Manage physical servers
- **IP Management**: Handle IP address allocation

### System Statistics
- Total locations, racks, and servers
- IP address utilization
- Capacity utilization metrics

### Recent Activity
- Latest changes and actions
- Audit trail of recent operations
- User activity monitoring

## Location Management

Locations represent your data center infrastructure in a hierarchical structure.

### Location Hierarchy

The module supports four levels of hierarchy:

1. **Region**: Geographic regions (e.g., "North America", "Europe")
2. **Facility**: Data center facilities (e.g., "NYC-DC1", "London-DC2")
3. **Floor**: Building floors (e.g., "Floor 1", "Floor 2")
4. **Room**: Individual rooms (e.g., "Server Room A", "Network Room")

### Creating a Location

1. Navigate to **Locations** → **Add Location**
2. Fill in the required information:
   - **Name**: Unique identifier for the location
   - **Type**: Select from Region, Facility, Floor, or Room
   - **Parent Location**: Choose the parent in the hierarchy
   - **Status**: Active, Inactive, or Maintenance

3. **Address Information** (optional):
   - Complete address details
   - Contact information
   - Geographic coordinates

4. **Capacity Information**:
   - Total U capacity available
   - Power capacity in watts
   - Cooling capacity in BTU
   - Additional notes

### Managing Locations

#### Viewing Location Details
- Click on any location name to view detailed information
- See associated racks, servers, and utilization statistics
- Review capacity usage and availability

#### Editing Locations
- Click the edit button next to any location
- Modify any field except the location type
- Changes are logged in the audit trail

#### Deleting Locations
- Locations can only be deleted if they have no child locations or associated racks
- Use the delete button and confirm the action
- Deletion is permanent and logged

### Location Hierarchy Best Practices

1. **Start with Regions**: Create geographic regions first
2. **Add Facilities**: Create data center facilities under regions
3. **Define Floors**: Add building floors under facilities
4. **Create Rooms**: Add specific rooms under floors
5. **Maintain Consistency**: Use consistent naming conventions

## Rack Management

Racks represent the physical server racks in your data center locations.

### Creating a Rack

1. Navigate to **Racks** → **Add Rack**
2. Configure basic information:
   - **Name**: Unique rack identifier (e.g., "R01-A01")
   - **Location**: Select the room where the rack is located
   - **Description**: Additional details about the rack

3. **Physical Specifications**:
   - **Height**: Rack height in U (default: 42U)
   - **Power Capacity**: Maximum power in watts
   - **Cooling Capacity**: Cooling capacity in BTU
   - **Weight Limit**: Maximum weight in kilograms

4. **Position Information** (optional):
   - X/Y coordinates for floor plan integration
   - Physical position within the room

### Rack Visualization

The module provides visual representation of racks:

#### List View
- Tabular view of all racks
- Sortable columns for easy organization
- Quick access to rack actions

#### Visual Rack View
- Interactive 42U rack representation
- Color-coded U positions:
  - **Green**: Available space
  - **Blue**: Occupied by servers
  - **Yellow**: Reserved space
  - **Red**: Maintenance mode

### Managing Rack Space

#### Viewing Rack Occupancy
- Click on any rack to see detailed occupancy
- View which servers occupy which U positions
- See available space and capacity utilization

#### Reserving Rack Space
- Reserve U positions for future server installations
- Set reservation expiration dates
- Track reserved space in reports

## Server Management

Servers represent the physical hardware installed in your racks.

### Adding a Server

1. Navigate to **Servers** → **Add Server**
2. **Basic Information**:
   - **Hostname**: Server hostname or identifier
   - **Rack**: Select the rack where server is installed
   - **U Position**: Starting U position (bottom-up numbering)
   - **U Size**: Server height in U (1U, 2U, 4U, etc.)

3. **Hardware Specifications**:
   - **Manufacturer**: Server manufacturer
   - **Model**: Server model number
   - **Serial Number**: Hardware serial number
   - **Asset Tag**: Internal asset tracking number

4. **Technical Details**:
   - **CPU Specifications**: Processor details
   - **RAM**: Memory in GB
   - **Storage**: Storage configuration
   - **Power Consumption**: Power usage in watts

5. **Network Information**:
   - **Management IP**: Out-of-band management IP
   - **Primary IP**: Primary network IP address
   - **Operating System**: Installed OS

### Server Lifecycle Management

#### Server Status
- **Provisioning**: Server being prepared for deployment
- **Active**: Server in production use
- **Maintenance**: Server undergoing maintenance
- **Decommissioned**: Server removed from service
- **Failed**: Server experiencing hardware failure

#### Moving Servers
1. Select the server to move
2. Choose new rack and U position
3. System validates space availability
4. Move is logged in audit trail

### WHMCS Service Integration

#### Linking Servers to WHMCS Services
1. Edit an existing server
2. Select "Link to WHMCS Service"
3. Choose the appropriate hosting service
4. Save the association

Benefits of linking:
- Automatic server provisioning
- Client portal integration
- Billing synchronization
- Service lifecycle automation

## Switch Management

Network switches manage connectivity within your data center.

### Adding a Switch

1. Navigate to **Switches** → **Add Switch**
2. **Basic Information**:
   - **Name**: Switch identifier
   - **Location**: Data center location
   - **Rack**: Physical rack (optional)
   - **Manufacturer**: Switch manufacturer
   - **Model**: Switch model

3. **Network Configuration**:
   - **Port Count**: Number of network ports
   - **Management IP**: Switch management interface
   - **SNMP Community**: SNMP community string

4. **Physical Details**:
   - **U Position**: Rack position if rack-mounted
   - **U Size**: Switch height in U
   - **Power Consumption**: Power usage

### Port Management

#### Tracking Port Usage
- View all ports on a switch
- Track which ports are connected
- Monitor port utilization

#### Cable Management
- Document cable connections
- Track patch panel connections
- Maintain connectivity maps

## IP Address Management (IPAM)

The IPAM system manages IP address allocation and tracking.

### Subnet Management

#### Creating Subnets

1. Navigate to **IP Management** → **Add Subnet**
2. **Network Information**:
   - **Name**: Descriptive subnet name
   - **Network**: Network address (e.g., ***********)
   - **Prefix Length**: CIDR prefix (e.g., 24)
   - **IP Version**: IPv4 or IPv6

3. **Configuration**:
   - **Gateway**: Default gateway address
   - **VLAN ID**: Associated VLAN
   - **Location**: Physical location
   - **Parent Subnet**: For subnet hierarchy

#### Subnet Hierarchy
- Create parent/child subnet relationships
- Organize subnets by location or purpose
- Track subnet utilization across hierarchy

### IP Address Management

#### Automatic IP Assignment
- Enable auto-assignment in module settings
- IPs automatically allocated during server provisioning
- First available IP assigned from appropriate subnet

#### Manual IP Assignment
1. Navigate to subnet details
2. Click on available IP address
3. Assign to server, client, or service
4. Add hostname and description

#### IP Address Status
- **Available**: Unassigned and ready for use
- **Assigned**: Currently in use
- **Reserved**: Reserved for specific purpose
- **Blacklisted**: Blocked from assignment

### VLAN Management

#### Associating VLANs with Subnets
- Assign VLAN IDs to subnets
- Track VLAN usage across locations
- Maintain VLAN documentation

## WHMCS Integration

The module integrates deeply with WHMCS for automated workflows.

### Service Linking

#### Linking Servers to Products
1. Create dedicated server products in WHMCS
2. Use custom fields to store DCIM server IDs
3. Link physical servers to client services

#### Automated Provisioning
- Configure hooks for automatic server assignment
- Trigger IP allocation on service activation
- Update server status based on service lifecycle

### Client Integration

#### IP Address Assignment
- Assign IP addresses to specific clients
- Link IPs to client services
- Track IP usage per client

#### Service Automation
- Automatic server provisioning
- IP allocation and release
- Status synchronization

## Reports and Analytics

### Capacity Reports
- Rack space utilization
- Power consumption analysis
- Cooling capacity usage
- Growth trend analysis

### Inventory Reports
- Complete hardware inventory
- Asset tracking reports
- Warranty expiration tracking
- Maintenance schedules

### Utilization Reports
- IP address utilization
- Subnet usage statistics
- Location capacity analysis
- Historical usage trends

### Audit Reports
- Complete change history
- User activity logs
- Compliance reporting
- Security audit trails

## Permissions and Security

### Role-Based Access Control

#### Permission Categories
- **View**: Read-only access to data
- **Create**: Ability to add new items
- **Edit**: Modify existing items
- **Delete**: Remove items from system

#### Module Sections
- Locations management
- Rack management
- Server management
- Switch management
- IP address management
- Reports access
- API access

### Configuring Permissions

1. Navigate to **DCIM** → **Permissions**
2. Select administrator role
3. Configure specific permissions
4. Save changes

### Audit Logging

#### What is Logged
- All create, update, delete operations
- User who performed the action
- Timestamp of the action
- Old and new values for changes
- IP address and user agent

#### Viewing Audit Logs
- Access through Reports section
- Filter by date, user, or action type
- Export logs for compliance

## Best Practices

### Data Organization

1. **Consistent Naming**: Use standardized naming conventions
2. **Hierarchy Planning**: Plan location hierarchy before implementation
3. **Documentation**: Maintain detailed notes and descriptions
4. **Regular Updates**: Keep information current and accurate

### Capacity Planning

1. **Monitor Utilization**: Regularly review capacity reports
2. **Plan Growth**: Use trends to predict future needs
3. **Reserve Space**: Maintain buffer capacity for growth
4. **Track Power**: Monitor power consumption trends

### Security

1. **Limit Access**: Grant minimum necessary permissions
2. **Regular Audits**: Review access logs regularly
3. **Strong Passwords**: Enforce strong password policies
4. **API Security**: Secure API keys and limit access

### Maintenance

1. **Regular Backups**: Backup DCIM data regularly
2. **Update Status**: Keep server and rack status current
3. **Clean Data**: Remove obsolete entries
4. **Monitor Logs**: Review error logs regularly

## Troubleshooting

### Common Issues

#### Module Access Problems
- **Symptom**: Cannot access DCIM module
- **Solution**: Check administrator role permissions
- **Check**: Verify module is activated and configured

#### Data Not Displaying
- **Symptom**: Empty lists or missing data
- **Solution**: Check database connectivity
- **Check**: Verify table permissions and data integrity

#### Performance Issues
- **Symptom**: Slow loading times
- **Solution**: Check database indexes and server resources
- **Check**: Review query performance and optimize

#### Integration Problems
- **Symptom**: WHMCS integration not working
- **Solution**: Verify service linking configuration
- **Check**: Review hook configuration and API access

### Getting Help

#### Support Channels
- **Email Support**: <EMAIL>
- **Documentation**: Online knowledge base
- **Community Forum**: User community discussions
- **Training**: Available training sessions

#### Before Contacting Support
1. Check this user manual
2. Review error logs
3. Verify permissions and configuration
4. Document steps to reproduce issues
5. Gather system information (WHMCS version, PHP version, etc.)

---

This user manual provides comprehensive guidance for using the DCIM module effectively. For additional support or advanced configuration, please contact our technical support team.
