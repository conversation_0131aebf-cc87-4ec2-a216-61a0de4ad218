# DCIM (Data Center Infrastructure Management) Addon Module for WHMCS

## Project Overview

This comprehensive DCIM addon module provides hosting providers and data center operators with complete physical infrastructure management capabilities integrated directly into the WHMCS admin interface.

## Technical Architecture

### Module Structure
```
modules/addons/dcim/
├── dcim.php                    # Main module file
├── hooks.php                   # WHMCS hooks for automation
├── whmcs.json                  # Module metadata
├── lib/                        # Core library files
│   ├── Admin/                  # Admin area controllers
│   │   ├── LocationController.php
│   │   ├── RackController.php
│   │   ├── ServerController.php
│   │   ├── SwitchController.php
│   │   └── IpamController.php
│   ├── Client/                 # Client area controllers
│   │   └── Controller.php
│   ├── Api/                    # API endpoints
│   │   ├── LocationApi.php
│   │   ├── RackApi.php
│   │   ├── ServerApi.php
│   │   ├── SwitchApi.php
│   │   └── IpamApi.php
│   ├── Models/                 # Data models
│   │   ├── Location.php
│   │   ├── Rack.php
│   │   ├── Server.php
│   │   ├── Switch.php
│   │   ├── IpSubnet.php
│   │   └── IpAddress.php
│   ├── Services/               # Business logic services
│   │   ├── LocationService.php
│   │   ├── RackService.php
│   │   ├── ServerService.php
│   │   ├── SwitchService.php
│   │   └── IpamService.php
│   └── Utils/                  # Utility classes
│       ├── DatabaseHelper.php
│       ├── ValidationHelper.php
│       └── PermissionHelper.php
├── templates/                  # Admin interface templates
│   ├── locations.tpl
│   ├── racks.tpl
│   ├── servers.tpl
│   ├── switches.tpl
│   ├── ipam.tpl
│   └── dashboard.tpl
├── assets/                     # Static assets
│   ├── css/
│   │   └── dcim.css
│   ├── js/
│   │   ├── dcim.js
│   │   ├── rack-visualizer.js
│   │   └── ip-calculator.js
│   └── images/
│       └── logo.png
├── lang/                       # Language files
│   ├── english.php
│   └── overrides/
└── docs/                       # Documentation
    ├── installation.md
    ├── user-manual.md
    └── api-documentation.md
```

## Database Schema Design

### Core Tables

#### 1. dcim_locations
```sql
CREATE TABLE `dcim_locations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `parent_id` int(11) DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `type` enum('region','facility','floor','room') NOT NULL DEFAULT 'facility',
  `address` text,
  `city` varchar(100),
  `state` varchar(100),
  `country` varchar(100),
  `postal_code` varchar(20),
  `contact_name` varchar(255),
  `contact_email` varchar(255),
  `contact_phone` varchar(50),
  `total_u_capacity` int(11) DEFAULT 0,
  `power_capacity_watts` int(11) DEFAULT 0,
  `cooling_capacity_btu` int(11) DEFAULT 0,
  `notes` text,
  `status` enum('active','inactive','maintenance') DEFAULT 'active',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_type` (`type`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_location_parent` FOREIGN KEY (`parent_id`) REFERENCES `dcim_locations` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

#### 2. dcim_racks
```sql
CREATE TABLE `dcim_racks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `location_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text,
  `height_u` int(11) NOT NULL DEFAULT 42,
  `power_capacity_watts` int(11) DEFAULT 0,
  `cooling_capacity_btu` int(11) DEFAULT 0,
  `weight_limit_kg` int(11) DEFAULT 0,
  `position_x` decimal(10,2) DEFAULT NULL,
  `position_y` decimal(10,2) DEFAULT NULL,
  `status` enum('active','inactive','maintenance','reserved') DEFAULT 'active',
  `notes` text,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_location_id` (`location_id`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_rack_location` FOREIGN KEY (`location_id`) REFERENCES `dcim_locations` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

#### 3. dcim_servers
```sql
CREATE TABLE `dcim_servers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `rack_id` int(11) NOT NULL,
  `whmcs_service_id` int(11) DEFAULT NULL,
  `hostname` varchar(255) NOT NULL,
  `asset_tag` varchar(100),
  `serial_number` varchar(255),
  `manufacturer` varchar(100),
  `model` varchar(100),
  `u_position` int(11) NOT NULL,
  `u_size` int(11) NOT NULL DEFAULT 1,
  `cpu_specs` text,
  `ram_gb` int(11) DEFAULT 0,
  `storage_specs` text,
  `power_consumption_watts` int(11) DEFAULT 0,
  `management_ip` varchar(45),
  `primary_ip` varchar(45),
  `status` enum('provisioning','active','maintenance','decommissioned','failed') DEFAULT 'provisioning',
  `os` varchar(100),
  `notes` text,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_rack_position` (`rack_id`, `u_position`),
  KEY `idx_whmcs_service_id` (`whmcs_service_id`),
  KEY `idx_hostname` (`hostname`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_server_rack` FOREIGN KEY (`rack_id`) REFERENCES `dcim_racks` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_server_whmcs_service` FOREIGN KEY (`whmcs_service_id`) REFERENCES `tblhosting` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

#### 4. dcim_switches
```sql
CREATE TABLE `dcim_switches` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `location_id` int(11) NOT NULL,
  `rack_id` int(11) DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `manufacturer` varchar(100),
  `model` varchar(100),
  `serial_number` varchar(255),
  `port_count` int(11) DEFAULT 0,
  `management_ip` varchar(45),
  `snmp_community` varchar(100),
  `u_position` int(11) DEFAULT NULL,
  `u_size` int(11) DEFAULT 1,
  `power_consumption_watts` int(11) DEFAULT 0,
  `status` enum('active','inactive','maintenance','failed') DEFAULT 'active',
  `notes` text,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_location_id` (`location_id`),
  KEY `idx_rack_id` (`rack_id`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_switch_location` FOREIGN KEY (`location_id`) REFERENCES `dcim_locations` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_switch_rack` FOREIGN KEY (`rack_id`) REFERENCES `dcim_racks` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

#### 5. dcim_ip_subnets
```sql
CREATE TABLE `dcim_ip_subnets` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `parent_id` int(11) DEFAULT NULL,
  `location_id` int(11) DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `network` varchar(45) NOT NULL,
  `prefix_length` int(11) NOT NULL,
  `ip_version` enum('4','6') NOT NULL DEFAULT '4',
  `gateway` varchar(45),
  `vlan_id` int(11) DEFAULT NULL,
  `description` text,
  `status` enum('active','inactive','reserved') DEFAULT 'active',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_network_prefix` (`network`, `prefix_length`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_location_id` (`location_id`),
  KEY `idx_vlan_id` (`vlan_id`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_subnet_parent` FOREIGN KEY (`parent_id`) REFERENCES `dcim_ip_subnets` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_subnet_location` FOREIGN KEY (`location_id`) REFERENCES `dcim_locations` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

#### 6. dcim_ip_addresses
```sql
CREATE TABLE `dcim_ip_addresses` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `subnet_id` int(11) NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `hostname` varchar(255),
  `assigned_to_type` enum('server','client','service','switch','other') DEFAULT NULL,
  `assigned_to_id` int(11) DEFAULT NULL,
  `whmcs_client_id` int(11) DEFAULT NULL,
  `whmcs_service_id` int(11) DEFAULT NULL,
  `status` enum('available','assigned','reserved','blacklisted') DEFAULT 'available',
  `description` text,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_ip_address` (`ip_address`),
  KEY `idx_subnet_id` (`subnet_id`),
  KEY `idx_assigned_to` (`assigned_to_type`, `assigned_to_id`),
  KEY `idx_whmcs_client_id` (`whmcs_client_id`),
  KEY `idx_whmcs_service_id` (`whmcs_service_id`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_ip_subnet` FOREIGN KEY (`subnet_id`) REFERENCES `dcim_ip_subnets` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_ip_whmcs_client` FOREIGN KEY (`whmcs_client_id`) REFERENCES `tblclients` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_ip_whmcs_service` FOREIGN KEY (`whmcs_service_id`) REFERENCES `tblhosting` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

#### 7. dcim_audit_log
```sql
CREATE TABLE `dcim_audit_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `admin_id` int(11) DEFAULT NULL,
  `action` varchar(100) NOT NULL,
  `table_name` varchar(100) NOT NULL,
  `record_id` int(11) NOT NULL,
  `old_values` json DEFAULT NULL,
  `new_values` json DEFAULT NULL,
  `ip_address` varchar(45),
  `user_agent` text,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_admin_id` (`admin_id`),
  KEY `idx_action` (`action`),
  KEY `idx_table_record` (`table_name`, `record_id`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_audit_admin` FOREIGN KEY (`admin_id`) REFERENCES `tbladmins` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

#### 8. dcim_permissions
```sql
CREATE TABLE `dcim_permissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `admin_role_id` int(11) NOT NULL,
  `permission` varchar(100) NOT NULL,
  `allowed` tinyint(1) DEFAULT 1,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_role_permission` (`admin_role_id`, `permission`),
  KEY `idx_admin_role_id` (`admin_role_id`),
  CONSTRAINT `fk_permission_role` FOREIGN KEY (`admin_role_id`) REFERENCES `tbladminroles` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

## Core Features and Functionality

### 1. Location Management
- **Hierarchical Structure**: Support for regions > facilities > floors > rooms
- **Complete CRUD Operations**: Create, read, update, delete locations
- **Capacity Tracking**: Total U-space, power, and cooling capacity
- **Contact Management**: Store contact information for each location
- **Status Management**: Active, inactive, maintenance states

### 2. Rack Management
- **Visual U-Space Representation**: Interactive 42U rack visualization
- **Capacity Tracking**: Real-time occupancy and available space
- **Physical Specifications**: Height, power, cooling, weight limits
- **Position Mapping**: X/Y coordinates for floor plan integration
- **Reservation System**: Reserve racks for future use

### 3. Server Management
- **Lifecycle Management**: Provisioning to decommissioning workflow
- **Hardware Tracking**: CPU, RAM, storage, serial numbers
- **Rack Assignment**: Precise U-space allocation with conflict detection
- **WHMCS Integration**: Link to WHMCS products and services
- **Status Tracking**: Provisioning, active, maintenance, failed states
- **Move History**: Audit trail of server relocations

### 4. Network Switch Management
- **Hierarchical Organization**: Location and rack-based organization
- **Port Management**: Track port count and connectivity
- **SNMP Integration**: Management IP and community strings
- **Stack Configuration**: Support for switch stacking
- **Cable Management**: Port assignment and connectivity mapping

### 5. IPAM (IP Address Management)
- **Subnet Hierarchy**: Parent/child subnet relationships
- **IPv4/IPv6 Support**: Dual-stack IP management
- **Automatic Allocation**: Intelligent IP assignment algorithms
- **WHMCS Integration**: Link IPs to clients and services
- **VLAN Association**: VLAN ID tracking and management
- **Bulk Operations**: Import/export and bulk assignment tools
- **DNS Integration**: Hostname and reverse DNS management

### 6. WHMCS Integration
- **Bidirectional Linking**: Connect physical assets to WHMCS entities
- **Automated Provisioning**: Hook-based automation for service lifecycle
- **Client Association**: Link IP addresses and servers to clients
- **Service Integration**: Connect servers to hosting products
- **Custom Fields**: Additional metadata storage via WHMCS custom fields

### 7. API Endpoints
- **RESTful Architecture**: Standard HTTP methods and status codes
- **Authentication**: API key-based authentication system
- **Comprehensive Coverage**: All CRUD operations via API
- **Rate Limiting**: Prevent API abuse with rate limiting
- **Documentation**: OpenAPI/Swagger documentation

### 8. Security and Permissions
- **Role-Based Access Control**: Granular permission matrix
- **Audit Logging**: Complete audit trail of all changes
- **Input Validation**: Comprehensive validation and sanitization
- **SQL Injection Prevention**: Prepared statements throughout
- **XSS Protection**: Output encoding and CSP headers

## Technical Requirements

### WHMCS Compatibility
- **Minimum Version**: WHMCS 8.0+
- **PHP Version**: PHP 7.4+ (8.0+ recommended)
- **Database**: MySQL 5.7+ or MariaDB 10.2+
- **Extensions**: PDO, JSON, cURL, OpenSSL

### Performance Considerations
- **Database Indexing**: Optimized indexes for all queries
- **Caching**: Redis/Memcached support for performance
- **Pagination**: Efficient pagination for large datasets
- **Lazy Loading**: On-demand loading of related data

### Scalability Features
- **Multi-Tenant**: Support for multiple data centers
- **Bulk Operations**: Efficient handling of large datasets
- **Background Jobs**: Queue-based processing for heavy operations
- **API Rate Limiting**: Prevent system overload
