<?php

namespace WHMCS\Module\Addon\Dcim\Admin;

use WHMCS\Module\Addon\Dcim\Services\LocationService;
use WHMCS\Module\Addon\Dcim\Utils\PermissionHelper;

/**
 * Location Controller Class
 * 
 * Handles admin area location management requests
 */
class LocationController
{
    private $locationService;
    
    public function __construct()
    {
        require_once __DIR__ . '/../Services/LocationService.php';
        require_once __DIR__ . '/../Models/Location.php';
        $this->locationService = new LocationService();
    }
    
    /**
     * Handle location management requests
     * 
     * @param array $vars Module variables
     * @return string HTML output
     */
    public function handle($vars)
    {
        $modulelink = $vars['modulelink'];
        $action = $_REQUEST['sub_action'] ?? 'list';
        
        try {
            switch ($action) {
                case 'list':
                    return $this->listLocations($modulelink);
                    
                case 'create':
                    return $this->createLocation($modulelink);
                    
                case 'edit':
                    return $this->editLocation($modulelink);
                    
                case 'delete':
                    return $this->deleteLocation($modulelink);
                    
                case 'view':
                    return $this->viewLocation($modulelink);
                    
                default:
                    return $this->listLocations($modulelink);
            }
        } catch (\Exception $e) {
            return '<div class="alert alert-danger">Error: ' . htmlspecialchars($e->getMessage()) . '</div>';
        }
    }
    
    /**
     * List locations
     * 
     * @param string $modulelink Module link
     * @return string HTML output
     */
    private function listLocations($modulelink)
    {
        // Get request parameters
        $params = [
            'page' => $_GET['page'] ?? 1,
            'limit' => $_GET['limit'] ?? 25,
            'search' => $_GET['search'] ?? '',
            'type' => $_GET['type'] ?? '',
            'status' => $_GET['status'] ?? '',
            'order_by' => $_GET['order_by'] ?? 'name',
            'order_dir' => $_GET['order_dir'] ?? 'asc',
        ];
        
        // Get locations data
        $result = $this->locationService->getLocationsList($params);
        $locations = $result['locations'];
        $pagination = $result['pagination'];
        
        // Build filter form
        $filterForm = $this->buildFilterForm($modulelink, $params);
        
        // Build locations table
        $locationsTable = $this->buildLocationsTable($modulelink, $locations, $params);
        
        // Build pagination
        $paginationHtml = $this->buildPagination($modulelink, $pagination, $params);
        
        $html = '
        <div class="dcim-locations">
            <div class="row">
                <div class="col-md-12">
                    <h2><i class="fa fa-building"></i> Location Management</h2>
                    <p class="text-muted">Manage data center locations with hierarchical structure support.</p>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-12">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <div class="row">
                                <div class="col-md-6">
                                    <h3 class="panel-title">Locations</h3>
                                </div>
                                <div class="col-md-6 text-right">';
        
        if (PermissionHelper::hasPermission('dcim.locations.create')) {
            $html .= '
                                    <a href="' . $modulelink . '&action=locations&sub_action=create" class="btn btn-success btn-sm">
                                        <i class="fa fa-plus"></i> Add Location
                                    </a>';
        }
        
        $html .= '
                                </div>
                            </div>
                        </div>
                        
                        <div class="panel-body">
                            ' . $filterForm . '
                            ' . $locationsTable . '
                            ' . $paginationHtml . '
                        </div>
                    </div>
                </div>
            </div>
        </div>';
        
        return $html;
    }
    
    /**
     * Create location form
     * 
     * @param string $modulelink Module link
     * @return string HTML output
     */
    private function createLocation($modulelink)
    {
        $errors = [];
        $formData = [];
        
        // Handle form submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $formData = $_POST;
            $result = $this->locationService->createLocation($formData);
            
            if ($result['success']) {
                header('Location: ' . $modulelink . '&action=locations&created=1');
                exit;
            } else {
                $errors = $result['errors'];
            }
        }
        
        return $this->renderLocationForm($modulelink, 'create', $formData, $errors);
    }
    
    /**
     * Edit location form
     * 
     * @param string $modulelink Module link
     * @return string HTML output
     */
    private function editLocation($modulelink)
    {
        $id = (int)($_GET['id'] ?? 0);
        if (!$id) {
            return '<div class="alert alert-danger">Invalid location ID.</div>';
        }
        
        $errors = [];
        $formData = [];
        
        try {
            $location = $this->locationService->getLocation($id);
            $formData = (array)$location;
        } catch (\Exception $e) {
            return '<div class="alert alert-danger">' . htmlspecialchars($e->getMessage()) . '</div>';
        }
        
        // Handle form submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $formData = array_merge($formData, $_POST);
            $result = $this->locationService->updateLocation($id, $_POST);
            
            if ($result['success']) {
                header('Location: ' . $modulelink . '&action=locations&updated=1');
                exit;
            } else {
                $errors = $result['errors'];
            }
        }
        
        return $this->renderLocationForm($modulelink, 'edit', $formData, $errors, $id);
    }
    
    /**
     * Delete location
     * 
     * @param string $modulelink Module link
     * @return string HTML output
     */
    private function deleteLocation($modulelink)
    {
        $id = (int)($_GET['id'] ?? 0);
        if (!$id) {
            return '<div class="alert alert-danger">Invalid location ID.</div>';
        }
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['confirm_delete'])) {
            $result = $this->locationService->deleteLocation($id);
            
            if ($result['success']) {
                header('Location: ' . $modulelink . '&action=locations&deleted=1');
                exit;
            } else {
                $errors = $result['errors'];
                $errorMsg = isset($errors['general']) ? $errors['general'] : 'Failed to delete location.';
                return '<div class="alert alert-danger">' . htmlspecialchars($errorMsg) . '</div>';
            }
        }
        
        try {
            $location = $this->locationService->getLocation($id);
        } catch (\Exception $e) {
            return '<div class="alert alert-danger">' . htmlspecialchars($e->getMessage()) . '</div>';
        }
        
        return '
        <div class="dcim-location-delete">
            <h2><i class="fa fa-trash"></i> Delete Location</h2>
            
            <div class="alert alert-warning">
                <strong>Warning:</strong> You are about to delete the location "' . htmlspecialchars($location->name) . '".
                This action cannot be undone.
            </div>
            
            <form method="post">
                <input type="hidden" name="confirm_delete" value="1">
                <button type="submit" class="btn btn-danger">
                    <i class="fa fa-trash"></i> Confirm Delete
                </button>
                <a href="' . $modulelink . '&action=locations" class="btn btn-default">
                    <i class="fa fa-arrow-left"></i> Cancel
                </a>
            </form>
        </div>';
    }
    
    /**
     * View location details
     * 
     * @param string $modulelink Module link
     * @return string HTML output
     */
    private function viewLocation($modulelink)
    {
        $id = (int)($_GET['id'] ?? 0);
        if (!$id) {
            return '<div class="alert alert-danger">Invalid location ID.</div>';
        }
        
        try {
            $location = $this->locationService->getLocation($id);
            $statistics = $this->locationService->getLocationStatistics($id);
        } catch (\Exception $e) {
            return '<div class="alert alert-danger">' . htmlspecialchars($e->getMessage()) . '</div>';
        }
        
        $html = '
        <div class="dcim-location-view">
            <div class="row">
                <div class="col-md-12">
                    <h2><i class="fa fa-building"></i> ' . htmlspecialchars($location->name) . '</h2>
                    <p class="text-muted">' . ucfirst($location->type) . ' Location</p>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-8">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h3 class="panel-title">Location Details</h3>
                        </div>
                        <div class="panel-body">
                            <dl class="dl-horizontal">
                                <dt>Name:</dt>
                                <dd>' . htmlspecialchars($location->name) . '</dd>
                                
                                <dt>Type:</dt>
                                <dd>' . ucfirst($location->type) . '</dd>
                                
                                <dt>Status:</dt>
                                <dd><span class="label label-' . ($location->status === 'active' ? 'success' : 'warning') . '">' . ucfirst($location->status) . '</span></dd>';
        
        if ($location->parent_name) {
            $html .= '
                                <dt>Parent Location:</dt>
                                <dd>' . htmlspecialchars($location->parent_name) . '</dd>';
        }
        
        if ($location->address) {
            $html .= '
                                <dt>Address:</dt>
                                <dd>' . nl2br(htmlspecialchars($location->address)) . '</dd>';
        }
        
        if ($location->city || $location->state || $location->country) {
            $html .= '
                                <dt>Location:</dt>
                                <dd>' . htmlspecialchars(trim($location->city . ', ' . $location->state . ' ' . $location->country, ', ')) . '</dd>';
        }
        
        if ($location->contact_name || $location->contact_email) {
            $html .= '
                                <dt>Contact:</dt>
                                <dd>' . htmlspecialchars($location->contact_name);
            if ($location->contact_email) {
                $html .= ' <a href="mailto:' . htmlspecialchars($location->contact_email) . '">' . htmlspecialchars($location->contact_email) . '</a>';
            }
            $html .= '</dd>';
        }
        
        $html .= '
                            </dl>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="panel panel-info">
                        <div class="panel-heading">
                            <h3 class="panel-title">Statistics</h3>
                        </div>
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-xs-6">
                                    <div class="text-center">
                                        <h4>' . $statistics['total_racks'] . '</h4>
                                        <small>Total Racks</small>
                                    </div>
                                </div>
                                <div class="col-xs-6">
                                    <div class="text-center">
                                        <h4>' . $statistics['total_servers'] . '</h4>
                                        <small>Total Servers</small>
                                    </div>
                                </div>
                            </div>
                            <hr>
                            <div class="row">
                                <div class="col-xs-12">
                                    <strong>U Capacity:</strong> ' . $statistics['used_u_capacity'] . ' / ' . $statistics['total_u_capacity'] . ' U<br>
                                    <strong>Power Usage:</strong> ' . number_format($statistics['power_usage']) . ' / ' . number_format($statistics['power_capacity']) . ' W
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h3 class="panel-title">Actions</h3>
                        </div>
                        <div class="panel-body">';
        
        if (PermissionHelper::hasPermission('dcim.locations.edit')) {
            $html .= '
                            <a href="' . $modulelink . '&action=locations&sub_action=edit&id=' . $location->id . '" class="btn btn-primary btn-sm btn-block">
                                <i class="fa fa-edit"></i> Edit Location
                            </a>';
        }
        
        if (PermissionHelper::hasPermission('dcim.locations.delete')) {
            $html .= '
                            <a href="' . $modulelink . '&action=locations&sub_action=delete&id=' . $location->id . '" class="btn btn-danger btn-sm btn-block">
                                <i class="fa fa-trash"></i> Delete Location
                            </a>';
        }
        
        $html .= '
                            <a href="' . $modulelink . '&action=locations" class="btn btn-default btn-sm btn-block">
                                <i class="fa fa-arrow-left"></i> Back to List
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>';
        
        return $html;
    }
    
    /**
     * Build filter form
     * 
     * @param string $modulelink Module link
     * @param array $params Current parameters
     * @return string HTML
     */
    private function buildFilterForm($modulelink, $params)
    {
        return '
        <form method="get" class="form-inline dcim-filter-form">
            <input type="hidden" name="module" value="dcim">
            <input type="hidden" name="action" value="locations">
            
            <div class="form-group">
                <input type="text" name="search" class="form-control" placeholder="Search locations..." value="' . htmlspecialchars($params['search']) . '">
            </div>
            
            <div class="form-group">
                <select name="type" class="form-control">
                    <option value="">All Types</option>
                    <option value="region"' . ($params['type'] === 'region' ? ' selected' : '') . '>Region</option>
                    <option value="facility"' . ($params['type'] === 'facility' ? ' selected' : '') . '>Facility</option>
                    <option value="floor"' . ($params['type'] === 'floor' ? ' selected' : '') . '>Floor</option>
                    <option value="room"' . ($params['type'] === 'room' ? ' selected' : '') . '>Room</option>
                </select>
            </div>
            
            <div class="form-group">
                <select name="status" class="form-control">
                    <option value="">All Statuses</option>
                    <option value="active"' . ($params['status'] === 'active' ? ' selected' : '') . '>Active</option>
                    <option value="inactive"' . ($params['status'] === 'inactive' ? ' selected' : '') . '>Inactive</option>
                    <option value="maintenance"' . ($params['status'] === 'maintenance' ? ' selected' : '') . '>Maintenance</option>
                </select>
            </div>
            
            <button type="submit" class="btn btn-default">
                <i class="fa fa-search"></i> Filter
            </button>
            
            <a href="' . $modulelink . '&action=locations" class="btn btn-default">
                <i class="fa fa-refresh"></i> Reset
            </a>
        </form>
        <hr>';
    }

    /**
     * Build locations table
     *
     * @param string $modulelink Module link
     * @param array $locations Locations data
     * @param array $params Current parameters
     * @return string HTML
     */
    private function buildLocationsTable($modulelink, $locations, $params)
    {
        $html = '
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th><a href="' . $this->buildSortLink($modulelink, 'name', $params) . '">Name</a></th>
                        <th><a href="' . $this->buildSortLink($modulelink, 'type', $params) . '">Type</a></th>
                        <th>Parent</th>
                        <th><a href="' . $this->buildSortLink($modulelink, 'city', $params) . '">City</a></th>
                        <th><a href="' . $this->buildSortLink($modulelink, 'status', $params) . '">Status</a></th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>';

        if (empty($locations)) {
            $html .= '
                    <tr>
                        <td colspan="6" class="text-center text-muted">No locations found.</td>
                    </tr>';
        } else {
            foreach ($locations as $location) {
                $html .= '
                    <tr>
                        <td>
                            <strong>' . htmlspecialchars($location->name) . '</strong>
                        </td>
                        <td>
                            <span class="label label-default">' . ucfirst($location->type) . '</span>
                        </td>
                        <td>' . ($location->parent_name ? htmlspecialchars($location->parent_name) : '-') . '</td>
                        <td>' . htmlspecialchars($location->city ?: '-') . '</td>
                        <td>
                            <span class="label label-' . ($location->status === 'active' ? 'success' : 'warning') . '">
                                ' . ucfirst($location->status) . '
                            </span>
                        </td>
                        <td>
                            <div class="btn-group btn-group-xs">
                                <a href="' . $modulelink . '&action=locations&sub_action=view&id=' . $location->id . '"
                                   class="btn btn-default" title="View">
                                    <i class="fa fa-eye"></i>
                                </a>';

                if (PermissionHelper::hasPermission('dcim.locations.edit')) {
                    $html .= '
                                <a href="' . $modulelink . '&action=locations&sub_action=edit&id=' . $location->id . '"
                                   class="btn btn-primary" title="Edit">
                                    <i class="fa fa-edit"></i>
                                </a>';
                }

                if (PermissionHelper::hasPermission('dcim.locations.delete')) {
                    $html .= '
                                <a href="' . $modulelink . '&action=locations&sub_action=delete&id=' . $location->id . '"
                                   class="btn btn-danger" title="Delete">
                                    <i class="fa fa-trash"></i>
                                </a>';
                }

                $html .= '
                            </div>
                        </td>
                    </tr>';
            }
        }

        $html .= '
                </tbody>
            </table>
        </div>';

        return $html;
    }

    /**
     * Build pagination
     *
     * @param string $modulelink Module link
     * @param array $pagination Pagination data
     * @param array $params Current parameters
     * @return string HTML
     */
    private function buildPagination($modulelink, $pagination, $params)
    {
        if ($pagination['total_pages'] <= 1) {
            return '';
        }

        $html = '<nav><ul class="pagination">';

        // Previous page
        if ($pagination['has_prev']) {
            $prevPage = $pagination['current_page'] - 1;
            $html .= '<li><a href="' . $this->buildPageLink($modulelink, $prevPage, $params) . '">&laquo; Previous</a></li>';
        } else {
            $html .= '<li class="disabled"><span>&laquo; Previous</span></li>';
        }

        // Page numbers
        $start = max(1, $pagination['current_page'] - 2);
        $end = min($pagination['total_pages'], $pagination['current_page'] + 2);

        for ($i = $start; $i <= $end; $i++) {
            if ($i == $pagination['current_page']) {
                $html .= '<li class="active"><span>' . $i . '</span></li>';
            } else {
                $html .= '<li><a href="' . $this->buildPageLink($modulelink, $i, $params) . '">' . $i . '</a></li>';
            }
        }

        // Next page
        if ($pagination['has_next']) {
            $nextPage = $pagination['current_page'] + 1;
            $html .= '<li><a href="' . $this->buildPageLink($modulelink, $nextPage, $params) . '">Next &raquo;</a></li>';
        } else {
            $html .= '<li class="disabled"><span>Next &raquo;</span></li>';
        }

        $html .= '</ul></nav>';

        return $html;
    }

    /**
     * Build sort link
     *
     * @param string $modulelink Module link
     * @param string $field Sort field
     * @param array $params Current parameters
     * @return string URL
     */
    private function buildSortLink($modulelink, $field, $params)
    {
        $newParams = $params;
        $newParams['order_by'] = $field;
        $newParams['order_dir'] = ($params['order_by'] === $field && $params['order_dir'] === 'asc') ? 'desc' : 'asc';

        return $modulelink . '&action=locations&' . http_build_query($newParams);
    }

    /**
     * Build page link
     *
     * @param string $modulelink Module link
     * @param int $page Page number
     * @param array $params Current parameters
     * @return string URL
     */
    private function buildPageLink($modulelink, $page, $params)
    {
        $newParams = $params;
        $newParams['page'] = $page;

        return $modulelink . '&action=locations&' . http_build_query($newParams);
    }

    /**
     * Render location form
     *
     * @param string $modulelink Module link
     * @param string $mode Form mode (create/edit)
     * @param array $formData Form data
     * @param array $errors Validation errors
     * @param int $id Location ID (for edit mode)
     * @return string HTML
     */
    private function renderLocationForm($modulelink, $mode, $formData, $errors, $id = null)
    {
        $isEdit = $mode === 'edit';
        $title = $isEdit ? 'Edit Location' : 'Create Location';
        $submitText = $isEdit ? 'Update Location' : 'Create Location';

        // Get parent locations for dropdown
        $parentOptions = $this->locationService->getLocationsForSelect(null, $id);

        $html = '
        <div class="dcim-location-form">
            <h2><i class="fa fa-building"></i> ' . $title . '</h2>';

        // Display errors
        if (!empty($errors)) {
            $html .= '<div class="alert alert-danger"><ul class="mb-0">';
            foreach ($errors as $field => $error) {
                $html .= '<li>' . htmlspecialchars($error) . '</li>';
            }
            $html .= '</ul></div>';
        }

        $html .= '
            <form method="post" class="form-horizontal">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title">Basic Information</h3>
                    </div>
                    <div class="panel-body">
                        <div class="form-group">
                            <label class="col-sm-2 control-label">Name *</label>
                            <div class="col-sm-10">
                                <input type="text" name="name" class="form-control" value="' . htmlspecialchars($formData['name'] ?? '') . '" required>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-2 control-label">Type *</label>
                            <div class="col-sm-10">
                                <select name="type" class="form-control" required>
                                    <option value="">Select Type</option>
                                    <option value="region"' . (($formData['type'] ?? '') === 'region' ? ' selected' : '') . '>Region</option>
                                    <option value="facility"' . (($formData['type'] ?? '') === 'facility' ? ' selected' : '') . '>Facility</option>
                                    <option value="floor"' . (($formData['type'] ?? '') === 'floor' ? ' selected' : '') . '>Floor</option>
                                    <option value="room"' . (($formData['type'] ?? '') === 'room' ? ' selected' : '') . '>Room</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-2 control-label">Parent Location</label>
                            <div class="col-sm-10">
                                <select name="parent_id" class="form-control">
                                    <option value="">No Parent</option>';

        foreach ($parentOptions as $optionId => $optionName) {
            $selected = ($formData['parent_id'] ?? '') == $optionId ? ' selected' : '';
            $html .= '<option value="' . $optionId . '"' . $selected . '>' . htmlspecialchars($optionName) . '</option>';
        }

        $html .= '
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-2 control-label">Status</label>
                            <div class="col-sm-10">
                                <select name="status" class="form-control">
                                    <option value="active"' . (($formData['status'] ?? 'active') === 'active' ? ' selected' : '') . '>Active</option>
                                    <option value="inactive"' . (($formData['status'] ?? '') === 'inactive' ? ' selected' : '') . '>Inactive</option>
                                    <option value="maintenance"' . (($formData['status'] ?? '') === 'maintenance' ? ' selected' : '') . '>Maintenance</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title">Address Information</h3>
                    </div>
                    <div class="panel-body">
                        <div class="form-group">
                            <label class="col-sm-2 control-label">Address</label>
                            <div class="col-sm-10">
                                <textarea name="address" class="form-control" rows="3">' . htmlspecialchars($formData['address'] ?? '') . '</textarea>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-2 control-label">City</label>
                            <div class="col-sm-10">
                                <input type="text" name="city" class="form-control" value="' . htmlspecialchars($formData['city'] ?? '') . '">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-2 control-label">State/Province</label>
                            <div class="col-sm-10">
                                <input type="text" name="state" class="form-control" value="' . htmlspecialchars($formData['state'] ?? '') . '">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-2 control-label">Country</label>
                            <div class="col-sm-10">
                                <input type="text" name="country" class="form-control" value="' . htmlspecialchars($formData['country'] ?? '') . '">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-2 control-label">Postal Code</label>
                            <div class="col-sm-10">
                                <input type="text" name="postal_code" class="form-control" value="' . htmlspecialchars($formData['postal_code'] ?? '') . '">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title">Contact Information</h3>
                    </div>
                    <div class="panel-body">
                        <div class="form-group">
                            <label class="col-sm-2 control-label">Contact Name</label>
                            <div class="col-sm-10">
                                <input type="text" name="contact_name" class="form-control" value="' . htmlspecialchars($formData['contact_name'] ?? '') . '">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-2 control-label">Contact Email</label>
                            <div class="col-sm-10">
                                <input type="email" name="contact_email" class="form-control" value="' . htmlspecialchars($formData['contact_email'] ?? '') . '">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-2 control-label">Contact Phone</label>
                            <div class="col-sm-10">
                                <input type="text" name="contact_phone" class="form-control" value="' . htmlspecialchars($formData['contact_phone'] ?? '') . '">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title">Capacity Information</h3>
                    </div>
                    <div class="panel-body">
                        <div class="form-group">
                            <label class="col-sm-2 control-label">Total U Capacity</label>
                            <div class="col-sm-10">
                                <input type="number" name="total_u_capacity" class="form-control" value="' . htmlspecialchars($formData['total_u_capacity'] ?? '0') . '" min="0">
                                <span class="help-block">Total rack units available in this location</span>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-2 control-label">Power Capacity (Watts)</label>
                            <div class="col-sm-10">
                                <input type="number" name="power_capacity_watts" class="form-control" value="' . htmlspecialchars($formData['power_capacity_watts'] ?? '0') . '" min="0">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-2 control-label">Cooling Capacity (BTU)</label>
                            <div class="col-sm-10">
                                <input type="number" name="cooling_capacity_btu" class="form-control" value="' . htmlspecialchars($formData['cooling_capacity_btu'] ?? '0') . '" min="0">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-2 control-label">Notes</label>
                            <div class="col-sm-10">
                                <textarea name="notes" class="form-control" rows="3">' . htmlspecialchars($formData['notes'] ?? '') . '</textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <div class="col-sm-offset-2 col-sm-10">
                        <button type="submit" class="btn btn-primary">
                            <i class="fa fa-save"></i> ' . $submitText . '
                        </button>
                        <a href="' . $modulelink . '&action=locations" class="btn btn-default">
                            <i class="fa fa-arrow-left"></i> Cancel
                        </a>
                    </div>
                </div>
            </form>
        </div>';

        return $html;
    }
}
