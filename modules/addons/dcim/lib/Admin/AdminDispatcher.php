<?php

namespace WHMCS\Module\Addon\Dcim\Admin;

use WHMCS\Module\Addon\Dcim\Utils\PermissionHelper;

/**
 * Admin Dispatcher Class
 * 
 * Handles routing and dispatching of admin area requests
 */
class AdminDispatcher
{
    /**
     * Dispatch request to appropriate controller
     * 
     * @param string $action Action to perform
     * @param array $vars Module variables
     * @return string HTML output
     */
    public function dispatch($action, $vars)
    {
        try {
            // Check if admin is logged in
            $admin = PermissionHelper::getCurrentAdmin();
            if (!$admin) {
                return $this->renderError('Access denied. Please log in as an administrator.');
            }
            
            // Route to appropriate controller method
            switch ($action) {
                case 'dashboard':
                case '':
                    return $this->dashboard($vars);
                    
                case 'locations':
                    return $this->handleLocations($vars);
                    
                case 'racks':
                    return $this->handleRacks($vars);
                    
                case 'servers':
                    return $this->handleServers($vars);
                    
                case 'switches':
                    return $this->handleSwitches($vars);
                    
                case 'ipam':
                    return $this->handleIpam($vars);
                    
                case 'reports':
                    return $this->handleReports($vars);
                    
                case 'settings':
                    return $this->handleSettings($vars);
                    
                case 'permissions':
                    return $this->handlePermissions($vars);
                    
                default:
                    return $this->renderError('Invalid action specified.');
            }
            
        } catch (\Exception $e) {
            return $this->renderError('Error: ' . $e->getMessage());
        }
    }
    
    /**
     * Display dashboard
     * 
     * @param array $vars Module variables
     * @return string HTML output
     */
    private function dashboard($vars)
    {
        $modulelink = $vars['modulelink'];
        
        // Get accessible menu items based on permissions
        $menuItems = PermissionHelper::getAccessibleMenuItems();
        
        $html = '
        <div class="dcim-dashboard">
            <div class="row">
                <div class="col-md-12">
                    <h1><i class="fa fa-building"></i> DCIM Dashboard</h1>
                    <p class="lead">Data Center Infrastructure Management</p>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-12">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h3 class="panel-title">Quick Access</h3>
                        </div>
                        <div class="panel-body">
                            <div class="row">';
        
        foreach ($menuItems as $item) {
            $html .= '
                            <div class="col-md-3 col-sm-6">
                                <div class="dcim-quick-action">
                                    <a href="' . $modulelink . '&action=' . $item['action'] . '" class="btn btn-default btn-lg btn-block">
                                        <i class="fa ' . $item['icon'] . ' fa-2x"></i><br>
                                        <strong>' . $item['name'] . '</strong><br>
                                        <small>' . $item['description'] . '</small>
                                    </a>
                                </div>
                            </div>';
        }
        
        $html .= '
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="panel panel-info">
                        <div class="panel-heading">
                            <h3 class="panel-title">System Overview</h3>
                        </div>
                        <div class="panel-body">
                            <div id="system-stats">
                                <p><i class="fa fa-spinner fa-spin"></i> Loading statistics...</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="panel panel-warning">
                        <div class="panel-heading">
                            <h3 class="panel-title">Recent Activity</h3>
                        </div>
                        <div class="panel-body">
                            <div id="recent-activity">
                                <p><i class="fa fa-spinner fa-spin"></i> Loading activity...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <style>
        .dcim-quick-action {
            margin-bottom: 20px;
        }
        .dcim-quick-action .btn {
            height: 120px;
            padding: 15px;
        }
        .dcim-quick-action .fa-2x {
            margin-bottom: 10px;
        }
        </style>
        
        <script>
        $(document).ready(function() {
            // Load system statistics
            loadSystemStats();
            
            // Load recent activity
            loadRecentActivity();
        });
        
        function loadSystemStats() {
            $.get("' . $modulelink . '&action=api&endpoint=stats", function(data) {
                if (data.success) {
                    var html = "";
                    html += "<div class=\"row\">";
                    html += "<div class=\"col-xs-6\"><strong>Locations:</strong><br>" + data.stats.locations + "</div>";
                    html += "<div class=\"col-xs-6\"><strong>Racks:</strong><br>" + data.stats.racks + "</div>";
                    html += "</div><hr>";
                    html += "<div class=\"row\">";
                    html += "<div class=\"col-xs-6\"><strong>Servers:</strong><br>" + data.stats.servers + "</div>";
                    html += "<div class=\"col-xs-6\"><strong>IP Addresses:</strong><br>" + data.stats.ip_addresses + "</div>";
                    html += "</div>";
                    $("#system-stats").html(html);
                } else {
                    $("#system-stats").html("<p class=\"text-muted\">Unable to load statistics</p>");
                }
            }).fail(function() {
                $("#system-stats").html("<p class=\"text-muted\">Unable to load statistics</p>");
            });
        }
        
        function loadRecentActivity() {
            $.get("' . $modulelink . '&action=api&endpoint=activity", function(data) {
                if (data.success && data.activity.length > 0) {
                    var html = "<ul class=\"list-unstyled\">";
                    $.each(data.activity.slice(0, 5), function(i, item) {
                        html += "<li><small class=\"text-muted\">" + item.created_at + "</small><br>";
                        html += item.username + " " + item.action + " " + item.table_name + "</li>";
                        if (i < 4) html += "<hr>";
                    });
                    html += "</ul>";
                    $("#recent-activity").html(html);
                } else {
                    $("#recent-activity").html("<p class=\"text-muted\">No recent activity</p>");
                }
            }).fail(function() {
                $("#recent-activity").html("<p class=\"text-muted\">Unable to load activity</p>");
            });
        }
        </script>';
        
        return $html;
    }
    
    /**
     * Handle locations management
     * 
     * @param array $vars Module variables
     * @return string HTML output
     */
    private function handleLocations($vars)
    {
        if (!PermissionHelper::hasPermission('dcim.locations.view')) {
            return $this->renderError('Access denied. You do not have permission to view locations.');
        }
        
        require_once __DIR__ . '/LocationController.php';
        $controller = new LocationController();
        return $controller->handle($vars);
    }
    
    /**
     * Handle racks management
     * 
     * @param array $vars Module variables
     * @return string HTML output
     */
    private function handleRacks($vars)
    {
        if (!PermissionHelper::hasPermission('dcim.racks.view')) {
            return $this->renderError('Access denied. You do not have permission to view racks.');
        }
        
        require_once __DIR__ . '/RackController.php';
        $controller = new RackController();
        return $controller->handle($vars);
    }
    
    /**
     * Handle servers management
     * 
     * @param array $vars Module variables
     * @return string HTML output
     */
    private function handleServers($vars)
    {
        if (!PermissionHelper::hasPermission('dcim.servers.view')) {
            return $this->renderError('Access denied. You do not have permission to view servers.');
        }
        
        require_once __DIR__ . '/ServerController.php';
        $controller = new ServerController();
        return $controller->handle($vars);
    }
    
    /**
     * Handle switches management
     * 
     * @param array $vars Module variables
     * @return string HTML output
     */
    private function handleSwitches($vars)
    {
        if (!PermissionHelper::hasPermission('dcim.switches.view')) {
            return $this->renderError('Access denied. You do not have permission to view switches.');
        }
        
        require_once __DIR__ . '/SwitchController.php';
        $controller = new SwitchController();
        return $controller->handle($vars);
    }
    
    /**
     * Handle IPAM management
     * 
     * @param array $vars Module variables
     * @return string HTML output
     */
    private function handleIpam($vars)
    {
        if (!PermissionHelper::hasPermission('dcim.ipam.view')) {
            return $this->renderError('Access denied. You do not have permission to view IP management.');
        }
        
        require_once __DIR__ . '/IpamController.php';
        $controller = new IpamController();
        return $controller->handle($vars);
    }
    
    /**
     * Handle reports
     * 
     * @param array $vars Module variables
     * @return string HTML output
     */
    private function handleReports($vars)
    {
        if (!PermissionHelper::hasPermission('dcim.reports.view')) {
            return $this->renderError('Access denied. You do not have permission to view reports.');
        }
        
        return '<div class="alert alert-info">Reports functionality coming soon...</div>';
    }
    
    /**
     * Handle settings
     * 
     * @param array $vars Module variables
     * @return string HTML output
     */
    private function handleSettings($vars)
    {
        // Only super admins can access settings
        $admin = PermissionHelper::getCurrentAdmin();
        if (!$admin || $admin['roleid'] != 1) {
            return $this->renderError('Access denied. Only super administrators can access settings.');
        }
        
        return '<div class="alert alert-info">Settings functionality coming soon...</div>';
    }
    
    /**
     * Handle permissions management
     * 
     * @param array $vars Module variables
     * @return string HTML output
     */
    private function handlePermissions($vars)
    {
        // Only super admins can manage permissions
        $admin = PermissionHelper::getCurrentAdmin();
        if (!$admin || $admin['roleid'] != 1) {
            return $this->renderError('Access denied. Only super administrators can manage permissions.');
        }
        
        return '<div class="alert alert-info">Permissions management functionality coming soon...</div>';
    }
    
    /**
     * Render error message
     * 
     * @param string $message Error message
     * @return string HTML error output
     */
    private function renderError($message)
    {
        return '<div class="alert alert-danger"><i class="fa fa-exclamation-triangle"></i> ' . htmlspecialchars($message) . '</div>';
    }
}
