<?php

namespace WHMCS\Module\Addon\Dcim\Utils;

use WHMCS\Database\Capsule;
use WHMCS\Authentication\CurrentUser;

/**
 * Permission Helper Class
 * 
 * Provides role-based access control utilities for the DCIM module
 */
class PermissionHelper
{
    /**
     * Available permissions in the DCIM module
     */
    const PERMISSIONS = [
        'dcim.locations.view' => 'View Locations',
        'dcim.locations.create' => 'Create Locations',
        'dcim.locations.edit' => 'Edit Locations',
        'dcim.locations.delete' => 'Delete Locations',
        'dcim.racks.view' => 'View Racks',
        'dcim.racks.create' => 'Create Racks',
        'dcim.racks.edit' => 'Edit Racks',
        'dcim.racks.delete' => 'Delete Racks',
        'dcim.servers.view' => 'View Servers',
        'dcim.servers.create' => 'Create Servers',
        'dcim.servers.edit' => 'Edit Servers',
        'dcim.servers.delete' => 'Delete Servers',
        'dcim.switches.view' => 'View Switches',
        'dcim.switches.create' => 'Create Switches',
        'dcim.switches.edit' => 'Edit Switches',
        'dcim.switches.delete' => 'Delete Switches',
        'dcim.ipam.view' => 'View IP Management',
        'dcim.ipam.create' => 'Create IP Resources',
        'dcim.ipam.edit' => 'Edit IP Resources',
        'dcim.ipam.delete' => 'Delete IP Resources',
        'dcim.reports.view' => 'View Reports',
        'dcim.api.access' => 'API Access',
    ];
    
    /**
     * Check if current admin has permission
     * 
     * @param string $permission Permission to check
     * @return bool
     */
    public static function hasPermission($permission)
    {
        try {
            $currentUser = new CurrentUser();
            $admin = $currentUser->admin();
            
            if (!$admin) {
                return false;
            }
            
            // Super admins have all permissions
            if ($admin->roleid == 1) {
                return true;
            }
            
            // Check specific permission
            $hasPermission = Capsule::table('dcim_permissions')
                ->where('admin_role_id', $admin->roleid)
                ->where('permission', $permission)
                ->where('allowed', 1)
                ->exists();
                
            return $hasPermission;
            
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * Check if current admin has any of the specified permissions
     * 
     * @param array $permissions Array of permissions to check
     * @return bool
     */
    public static function hasAnyPermission($permissions)
    {
        foreach ($permissions as $permission) {
            if (self::hasPermission($permission)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * Check if current admin has all of the specified permissions
     * 
     * @param array $permissions Array of permissions to check
     * @return bool
     */
    public static function hasAllPermissions($permissions)
    {
        foreach ($permissions as $permission) {
            if (!self::hasPermission($permission)) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * Get all permissions for a specific admin role
     * 
     * @param int $roleId Admin role ID
     * @return array Array of permissions
     */
    public static function getRolePermissions($roleId)
    {
        try {
            $permissions = Capsule::table('dcim_permissions')
                ->where('admin_role_id', $roleId)
                ->where('allowed', 1)
                ->pluck('permission')
                ->toArray();
                
            return $permissions;
            
        } catch (\Exception $e) {
            return [];
        }
    }
    
    /**
     * Set permissions for an admin role
     * 
     * @param int $roleId Admin role ID
     * @param array $permissions Array of permissions to set
     * @return bool Success status
     */
    public static function setRolePermissions($roleId, $permissions)
    {
        try {
            // Remove existing permissions for this role
            Capsule::table('dcim_permissions')
                ->where('admin_role_id', $roleId)
                ->delete();
            
            // Insert new permissions
            $insertData = [];
            foreach ($permissions as $permission) {
                if (array_key_exists($permission, self::PERMISSIONS)) {
                    $insertData[] = [
                        'admin_role_id' => $roleId,
                        'permission' => $permission,
                        'allowed' => 1,
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s'),
                    ];
                }
            }
            
            if (!empty($insertData)) {
                Capsule::table('dcim_permissions')->insert($insertData);
            }
            
            return true;
            
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * Get current admin information
     * 
     * @return array|null Admin information or null if not logged in
     */
    public static function getCurrentAdmin()
    {
        try {
            $currentUser = new CurrentUser();
            $admin = $currentUser->admin();
            
            if (!$admin) {
                return null;
            }
            
            return [
                'id' => $admin->id,
                'username' => $admin->username,
                'firstname' => $admin->firstname,
                'lastname' => $admin->lastname,
                'email' => $admin->email,
                'roleid' => $admin->roleid,
                'rolename' => $admin->role->name ?? 'Unknown',
            ];
            
        } catch (\Exception $e) {
            return null;
        }
    }
    
    /**
     * Check if current admin can access a specific action
     * 
     * @param string $module Module name (locations, racks, servers, etc.)
     * @param string $action Action name (view, create, edit, delete)
     * @return bool
     */
    public static function canAccess($module, $action)
    {
        $permission = "dcim.{$module}.{$action}";
        return self::hasPermission($permission);
    }
    
    /**
     * Require permission or throw exception
     * 
     * @param string $permission Permission to require
     * @throws \Exception If permission is not granted
     */
    public static function requirePermission($permission)
    {
        if (!self::hasPermission($permission)) {
            throw new \Exception("Access denied. Required permission: {$permission}");
        }
    }
    
    /**
     * Get permission-filtered menu items
     * 
     * @return array Array of menu items the current admin can access
     */
    public static function getAccessibleMenuItems()
    {
        $menuItems = [];
        
        if (self::hasPermission('dcim.locations.view')) {
            $menuItems[] = [
                'name' => 'Locations',
                'action' => 'locations',
                'icon' => 'fa-building',
                'description' => 'Manage data center locations'
            ];
        }
        
        if (self::hasPermission('dcim.racks.view')) {
            $menuItems[] = [
                'name' => 'Racks',
                'action' => 'racks',
                'icon' => 'fa-server',
                'description' => 'Manage server racks'
            ];
        }
        
        if (self::hasPermission('dcim.servers.view')) {
            $menuItems[] = [
                'name' => 'Servers',
                'action' => 'servers',
                'icon' => 'fa-desktop',
                'description' => 'Manage servers'
            ];
        }
        
        if (self::hasPermission('dcim.switches.view')) {
            $menuItems[] = [
                'name' => 'Switches',
                'action' => 'switches',
                'icon' => 'fa-sitemap',
                'description' => 'Manage network switches'
            ];
        }
        
        if (self::hasPermission('dcim.ipam.view')) {
            $menuItems[] = [
                'name' => 'IP Management',
                'action' => 'ipam',
                'icon' => 'fa-globe',
                'description' => 'Manage IP addresses and subnets'
            ];
        }
        
        if (self::hasPermission('dcim.reports.view')) {
            $menuItems[] = [
                'name' => 'Reports',
                'action' => 'reports',
                'icon' => 'fa-chart-bar',
                'description' => 'View system reports'
            ];
        }
        
        return $menuItems;
    }
    
    /**
     * Log admin action for audit trail
     * 
     * @param string $action Action performed
     * @param string $tableName Database table affected
     * @param int $recordId Record ID affected
     * @param array $oldValues Old values (for updates)
     * @param array $newValues New values
     * @return bool Success status
     */
    public static function logAction($action, $tableName, $recordId, $oldValues = null, $newValues = null)
    {
        try {
            $admin = self::getCurrentAdmin();
            
            if (!$admin) {
                return false;
            }
            
            Capsule::table('dcim_audit_log')->insert([
                'admin_id' => $admin['id'],
                'action' => $action,
                'table_name' => $tableName,
                'record_id' => $recordId,
                'old_values' => $oldValues ? json_encode($oldValues) : null,
                'new_values' => $newValues ? json_encode($newValues) : null,
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? null,
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null,
                'created_at' => date('Y-m-d H:i:s'),
            ]);
            
            return true;
            
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * Get audit log entries
     * 
     * @param array $filters Optional filters
     * @param int $limit Number of entries to return
     * @param int $offset Offset for pagination
     * @return array Audit log entries
     */
    public static function getAuditLog($filters = [], $limit = 50, $offset = 0)
    {
        try {
            $query = Capsule::table('dcim_audit_log as dal')
                ->leftJoin('tbladmins as ta', 'dal.admin_id', '=', 'ta.id')
                ->select([
                    'dal.*',
                    'ta.username',
                    'ta.firstname',
                    'ta.lastname'
                ])
                ->orderBy('dal.created_at', 'desc');
            
            // Apply filters
            if (!empty($filters['admin_id'])) {
                $query->where('dal.admin_id', $filters['admin_id']);
            }
            
            if (!empty($filters['action'])) {
                $query->where('dal.action', $filters['action']);
            }
            
            if (!empty($filters['table_name'])) {
                $query->where('dal.table_name', $filters['table_name']);
            }
            
            if (!empty($filters['date_from'])) {
                $query->where('dal.created_at', '>=', $filters['date_from']);
            }
            
            if (!empty($filters['date_to'])) {
                $query->where('dal.created_at', '<=', $filters['date_to']);
            }
            
            return $query->limit($limit)->offset($offset)->get()->toArray();
            
        } catch (\Exception $e) {
            return [];
        }
    }
}
