<?php

namespace WHMCS\Module\Addon\Dcim\Utils;

use WHMCS\Database\Capsule;

/**
 * Database Helper Class
 * 
 * Provides database schema creation and management utilities for the DCIM module
 */
class DatabaseHelper
{
    /**
     * Create all DCIM tables
     * 
     * @return array Result array with status and description
     */
    public static function createTables()
    {
        try {
            self::createLocationsTable();
            self::createRacksTable();
            self::createServersTable();
            self::createSwitchesTable();
            self::createIpSubnetsTable();
            self::createIpAddressesTable();
            self::createAuditLogTable();
            self::createPermissionsTable();
            
            return [
                'status' => 'success',
                'description' => 'All DCIM tables created successfully.'
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'description' => 'Failed to create DCIM tables: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Drop all DCIM tables
     * 
     * @return array Result array with status and description
     */
    public static function dropTables()
    {
        try {
            $tables = [
                'dcim_permissions',
                'dcim_audit_log',
                'dcim_ip_addresses',
                'dcim_ip_subnets',
                'dcim_switches',
                'dcim_servers',
                'dcim_racks',
                'dcim_locations'
            ];
            
            foreach ($tables as $table) {
                Capsule::schema()->dropIfExists($table);
            }
            
            return [
                'status' => 'success',
                'description' => 'All DCIM tables dropped successfully.'
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'description' => 'Failed to drop DCIM tables: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Create dcim_locations table
     */
    private static function createLocationsTable()
    {
        if (!Capsule::schema()->hasTable('dcim_locations')) {
            Capsule::schema()->create('dcim_locations', function ($table) {
                $table->increments('id');
                $table->integer('parent_id')->unsigned()->nullable();
                $table->string('name');
                $table->enum('type', ['region', 'facility', 'floor', 'room'])->default('facility');
                $table->text('address')->nullable();
                $table->string('city', 100)->nullable();
                $table->string('state', 100)->nullable();
                $table->string('country', 100)->nullable();
                $table->string('postal_code', 20)->nullable();
                $table->string('contact_name')->nullable();
                $table->string('contact_email')->nullable();
                $table->string('contact_phone', 50)->nullable();
                $table->integer('total_u_capacity')->default(0);
                $table->integer('power_capacity_watts')->default(0);
                $table->integer('cooling_capacity_btu')->default(0);
                $table->text('notes')->nullable();
                $table->enum('status', ['active', 'inactive', 'maintenance'])->default('active');
                $table->timestamps();
                
                $table->index('parent_id');
                $table->index('type');
                $table->index('status');
                $table->foreign('parent_id')->references('id')->on('dcim_locations')->onDelete('set null');
            });
        }
    }
    
    /**
     * Create dcim_racks table
     */
    private static function createRacksTable()
    {
        if (!Capsule::schema()->hasTable('dcim_racks')) {
            Capsule::schema()->create('dcim_racks', function ($table) {
                $table->increments('id');
                $table->integer('location_id')->unsigned();
                $table->string('name');
                $table->text('description')->nullable();
                $table->integer('height_u')->default(42);
                $table->integer('power_capacity_watts')->default(0);
                $table->integer('cooling_capacity_btu')->default(0);
                $table->integer('weight_limit_kg')->default(0);
                $table->decimal('position_x', 10, 2)->nullable();
                $table->decimal('position_y', 10, 2)->nullable();
                $table->enum('status', ['active', 'inactive', 'maintenance', 'reserved'])->default('active');
                $table->text('notes')->nullable();
                $table->timestamps();
                
                $table->index('location_id');
                $table->index('status');
                $table->foreign('location_id')->references('id')->on('dcim_locations')->onDelete('cascade');
            });
        }
    }
    
    /**
     * Create dcim_servers table
     */
    private static function createServersTable()
    {
        if (!Capsule::schema()->hasTable('dcim_servers')) {
            Capsule::schema()->create('dcim_servers', function ($table) {
                $table->increments('id');
                $table->integer('rack_id')->unsigned();
                $table->integer('whmcs_service_id')->unsigned()->nullable();
                $table->string('hostname');
                $table->string('asset_tag', 100)->nullable();
                $table->string('serial_number')->nullable();
                $table->string('manufacturer', 100)->nullable();
                $table->string('model', 100)->nullable();
                $table->integer('u_position');
                $table->integer('u_size')->default(1);
                $table->text('cpu_specs')->nullable();
                $table->integer('ram_gb')->default(0);
                $table->text('storage_specs')->nullable();
                $table->integer('power_consumption_watts')->default(0);
                $table->string('management_ip', 45)->nullable();
                $table->string('primary_ip', 45)->nullable();
                $table->enum('status', ['provisioning', 'active', 'maintenance', 'decommissioned', 'failed'])->default('provisioning');
                $table->string('os', 100)->nullable();
                $table->text('notes')->nullable();
                $table->timestamps();
                
                $table->unique(['rack_id', 'u_position']);
                $table->index('whmcs_service_id');
                $table->index('hostname');
                $table->index('status');
                $table->foreign('rack_id')->references('id')->on('dcim_racks')->onDelete('cascade');
            });
        }
    }
    
    /**
     * Create dcim_switches table
     */
    private static function createSwitchesTable()
    {
        if (!Capsule::schema()->hasTable('dcim_switches')) {
            Capsule::schema()->create('dcim_switches', function ($table) {
                $table->increments('id');
                $table->integer('location_id')->unsigned();
                $table->integer('rack_id')->unsigned()->nullable();
                $table->string('name');
                $table->string('manufacturer', 100)->nullable();
                $table->string('model', 100)->nullable();
                $table->string('serial_number')->nullable();
                $table->integer('port_count')->default(0);
                $table->string('management_ip', 45)->nullable();
                $table->string('snmp_community', 100)->nullable();
                $table->integer('u_position')->nullable();
                $table->integer('u_size')->default(1);
                $table->integer('power_consumption_watts')->default(0);
                $table->enum('status', ['active', 'inactive', 'maintenance', 'failed'])->default('active');
                $table->text('notes')->nullable();
                $table->timestamps();
                
                $table->index('location_id');
                $table->index('rack_id');
                $table->index('status');
                $table->foreign('location_id')->references('id')->on('dcim_locations')->onDelete('cascade');
                $table->foreign('rack_id')->references('id')->on('dcim_racks')->onDelete('set null');
            });
        }
    }
    
    /**
     * Create dcim_ip_subnets table
     */
    private static function createIpSubnetsTable()
    {
        if (!Capsule::schema()->hasTable('dcim_ip_subnets')) {
            Capsule::schema()->create('dcim_ip_subnets', function ($table) {
                $table->increments('id');
                $table->integer('parent_id')->unsigned()->nullable();
                $table->integer('location_id')->unsigned()->nullable();
                $table->string('name');
                $table->string('network', 45);
                $table->integer('prefix_length');
                $table->enum('ip_version', ['4', '6'])->default('4');
                $table->string('gateway', 45)->nullable();
                $table->integer('vlan_id')->nullable();
                $table->text('description')->nullable();
                $table->enum('status', ['active', 'inactive', 'reserved'])->default('active');
                $table->timestamps();
                
                $table->unique(['network', 'prefix_length']);
                $table->index('parent_id');
                $table->index('location_id');
                $table->index('vlan_id');
                $table->index('status');
                $table->foreign('parent_id')->references('id')->on('dcim_ip_subnets')->onDelete('set null');
                $table->foreign('location_id')->references('id')->on('dcim_locations')->onDelete('set null');
            });
        }
    }
    
    /**
     * Create dcim_ip_addresses table
     */
    private static function createIpAddressesTable()
    {
        if (!Capsule::schema()->hasTable('dcim_ip_addresses')) {
            Capsule::schema()->create('dcim_ip_addresses', function ($table) {
                $table->increments('id');
                $table->integer('subnet_id')->unsigned();
                $table->string('ip_address', 45);
                $table->string('hostname')->nullable();
                $table->enum('assigned_to_type', ['server', 'client', 'service', 'switch', 'other'])->nullable();
                $table->integer('assigned_to_id')->unsigned()->nullable();
                $table->integer('whmcs_client_id')->unsigned()->nullable();
                $table->integer('whmcs_service_id')->unsigned()->nullable();
                $table->enum('status', ['available', 'assigned', 'reserved', 'blacklisted'])->default('available');
                $table->text('description')->nullable();
                $table->timestamps();
                
                $table->unique('ip_address');
                $table->index('subnet_id');
                $table->index(['assigned_to_type', 'assigned_to_id']);
                $table->index('whmcs_client_id');
                $table->index('whmcs_service_id');
                $table->index('status');
                $table->foreign('subnet_id')->references('id')->on('dcim_ip_subnets')->onDelete('cascade');
            });
        }
    }
    
    /**
     * Create dcim_audit_log table
     */
    private static function createAuditLogTable()
    {
        if (!Capsule::schema()->hasTable('dcim_audit_log')) {
            Capsule::schema()->create('dcim_audit_log', function ($table) {
                $table->increments('id');
                $table->integer('admin_id')->unsigned()->nullable();
                $table->string('action', 100);
                $table->string('table_name', 100);
                $table->integer('record_id')->unsigned();
                $table->json('old_values')->nullable();
                $table->json('new_values')->nullable();
                $table->string('ip_address', 45)->nullable();
                $table->text('user_agent')->nullable();
                $table->timestamp('created_at')->useCurrent();
                
                $table->index('admin_id');
                $table->index('action');
                $table->index(['table_name', 'record_id']);
                $table->index('created_at');
            });
        }
    }
    
    /**
     * Create dcim_permissions table
     */
    private static function createPermissionsTable()
    {
        if (!Capsule::schema()->hasTable('dcim_permissions')) {
            Capsule::schema()->create('dcim_permissions', function ($table) {
                $table->increments('id');
                $table->integer('admin_role_id')->unsigned();
                $table->string('permission', 100);
                $table->boolean('allowed')->default(true);
                $table->timestamps();
                
                $table->unique(['admin_role_id', 'permission']);
                $table->index('admin_role_id');
            });
        }
    }
}
