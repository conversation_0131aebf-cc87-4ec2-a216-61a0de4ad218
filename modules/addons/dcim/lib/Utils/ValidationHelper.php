<?php

namespace WHMCS\Module\Addon\Dcim\Utils;

/**
 * Validation Helper Class
 * 
 * Provides input validation and sanitization utilities for the DCIM module
 */
class ValidationHelper
{
    /**
     * Validate IP address
     * 
     * @param string $ip IP address to validate
     * @param int $version IP version (4 or 6)
     * @return bool
     */
    public static function validateIpAddress($ip, $version = null)
    {
        if ($version === 4) {
            return filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4) !== false;
        } elseif ($version === 6) {
            return filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV6) !== false;
        } else {
            return filter_var($ip, FILTER_VALIDATE_IP) !== false;
        }
    }
    
    /**
     * Validate CIDR notation
     * 
     * @param string $cidr CIDR notation to validate
     * @param int $version IP version (4 or 6)
     * @return bool
     */
    public static function validateCidr($cidr, $version = null)
    {
        if (!preg_match('/^(.+)\/(\d+)$/', $cidr, $matches)) {
            return false;
        }
        
        $ip = $matches[1];
        $prefix = (int)$matches[2];
        
        if (!self::validateIpAddress($ip, $version)) {
            return false;
        }
        
        if ($version === 4 || filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
            return $prefix >= 0 && $prefix <= 32;
        } elseif ($version === 6 || filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV6)) {
            return $prefix >= 0 && $prefix <= 128;
        }
        
        return false;
    }
    
    /**
     * Validate hostname
     * 
     * @param string $hostname Hostname to validate
     * @return bool
     */
    public static function validateHostname($hostname)
    {
        if (strlen($hostname) > 253) {
            return false;
        }
        
        return preg_match('/^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$/', $hostname);
    }
    
    /**
     * Validate email address
     * 
     * @param string $email Email address to validate
     * @return bool
     */
    public static function validateEmail($email)
    {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }
    
    /**
     * Validate U position
     * 
     * @param int $position U position
     * @param int $rackHeight Rack height in U
     * @param int $serverSize Server size in U
     * @return bool
     */
    public static function validateUPosition($position, $rackHeight = 42, $serverSize = 1)
    {
        $position = (int)$position;
        $rackHeight = (int)$rackHeight;
        $serverSize = (int)$serverSize;
        
        return $position >= 1 && ($position + $serverSize - 1) <= $rackHeight;
    }
    
    /**
     * Validate VLAN ID
     * 
     * @param int $vlanId VLAN ID to validate
     * @return bool
     */
    public static function validateVlanId($vlanId)
    {
        $vlanId = (int)$vlanId;
        return $vlanId >= 1 && $vlanId <= 4094;
    }
    
    /**
     * Sanitize string input
     * 
     * @param string $input Input string
     * @param int $maxLength Maximum length
     * @return string
     */
    public static function sanitizeString($input, $maxLength = null)
    {
        $sanitized = trim(strip_tags($input));
        
        if ($maxLength && strlen($sanitized) > $maxLength) {
            $sanitized = substr($sanitized, 0, $maxLength);
        }
        
        return $sanitized;
    }
    
    /**
     * Sanitize HTML input
     * 
     * @param string $input Input HTML
     * @param array $allowedTags Allowed HTML tags
     * @return string
     */
    public static function sanitizeHtml($input, $allowedTags = ['p', 'br', 'strong', 'em', 'ul', 'ol', 'li'])
    {
        $allowedTagsString = '<' . implode('><', $allowedTags) . '>';
        return strip_tags($input, $allowedTagsString);
    }
    
    /**
     * Validate required fields
     * 
     * @param array $data Input data
     * @param array $requiredFields Required field names
     * @return array Validation errors
     */
    public static function validateRequired($data, $requiredFields)
    {
        $errors = [];
        
        foreach ($requiredFields as $field) {
            if (!isset($data[$field]) || trim($data[$field]) === '') {
                $errors[$field] = "Field '{$field}' is required.";
            }
        }
        
        return $errors;
    }
    
    /**
     * Validate location data
     * 
     * @param array $data Location data
     * @return array Validation errors
     */
    public static function validateLocationData($data)
    {
        $errors = [];
        
        // Required fields
        $requiredErrors = self::validateRequired($data, ['name', 'type']);
        $errors = array_merge($errors, $requiredErrors);
        
        // Validate type
        if (isset($data['type']) && !in_array($data['type'], ['region', 'facility', 'floor', 'room'])) {
            $errors['type'] = 'Invalid location type.';
        }
        
        // Validate email if provided
        if (!empty($data['contact_email']) && !self::validateEmail($data['contact_email'])) {
            $errors['contact_email'] = 'Invalid email address.';
        }
        
        // Validate numeric fields
        $numericFields = ['total_u_capacity', 'power_capacity_watts', 'cooling_capacity_btu'];
        foreach ($numericFields as $field) {
            if (isset($data[$field]) && !is_numeric($data[$field])) {
                $errors[$field] = "Field '{$field}' must be numeric.";
            }
        }
        
        return $errors;
    }
    
    /**
     * Validate rack data
     * 
     * @param array $data Rack data
     * @return array Validation errors
     */
    public static function validateRackData($data)
    {
        $errors = [];
        
        // Required fields
        $requiredErrors = self::validateRequired($data, ['name', 'location_id']);
        $errors = array_merge($errors, $requiredErrors);
        
        // Validate numeric fields
        $numericFields = ['location_id', 'height_u', 'power_capacity_watts', 'cooling_capacity_btu', 'weight_limit_kg'];
        foreach ($numericFields as $field) {
            if (isset($data[$field]) && !is_numeric($data[$field])) {
                $errors[$field] = "Field '{$field}' must be numeric.";
            }
        }
        
        // Validate height_u
        if (isset($data['height_u']) && ($data['height_u'] < 1 || $data['height_u'] > 100)) {
            $errors['height_u'] = 'Rack height must be between 1 and 100 U.';
        }
        
        // Validate status
        if (isset($data['status']) && !in_array($data['status'], ['active', 'inactive', 'maintenance', 'reserved'])) {
            $errors['status'] = 'Invalid rack status.';
        }
        
        return $errors;
    }
    
    /**
     * Validate server data
     * 
     * @param array $data Server data
     * @return array Validation errors
     */
    public static function validateServerData($data)
    {
        $errors = [];
        
        // Required fields
        $requiredErrors = self::validateRequired($data, ['hostname', 'rack_id', 'u_position', 'u_size']);
        $errors = array_merge($errors, $requiredErrors);
        
        // Validate hostname
        if (isset($data['hostname']) && !self::validateHostname($data['hostname'])) {
            $errors['hostname'] = 'Invalid hostname format.';
        }
        
        // Validate IP addresses
        if (!empty($data['management_ip']) && !self::validateIpAddress($data['management_ip'])) {
            $errors['management_ip'] = 'Invalid management IP address.';
        }
        
        if (!empty($data['primary_ip']) && !self::validateIpAddress($data['primary_ip'])) {
            $errors['primary_ip'] = 'Invalid primary IP address.';
        }
        
        // Validate numeric fields
        $numericFields = ['rack_id', 'u_position', 'u_size', 'ram_gb', 'power_consumption_watts'];
        foreach ($numericFields as $field) {
            if (isset($data[$field]) && !is_numeric($data[$field])) {
                $errors[$field] = "Field '{$field}' must be numeric.";
            }
        }
        
        // Validate U size
        if (isset($data['u_size']) && ($data['u_size'] < 1 || $data['u_size'] > 10)) {
            $errors['u_size'] = 'Server U size must be between 1 and 10.';
        }
        
        // Validate status
        if (isset($data['status']) && !in_array($data['status'], ['provisioning', 'active', 'maintenance', 'decommissioned', 'failed'])) {
            $errors['status'] = 'Invalid server status.';
        }
        
        return $errors;
    }
    
    /**
     * Validate IP subnet data
     * 
     * @param array $data IP subnet data
     * @return array Validation errors
     */
    public static function validateIpSubnetData($data)
    {
        $errors = [];
        
        // Required fields
        $requiredErrors = self::validateRequired($data, ['name', 'network', 'prefix_length', 'ip_version']);
        $errors = array_merge($errors, $requiredErrors);
        
        // Validate IP version
        if (isset($data['ip_version']) && !in_array($data['ip_version'], ['4', '6'])) {
            $errors['ip_version'] = 'IP version must be 4 or 6.';
        }
        
        // Validate network and prefix
        if (isset($data['network']) && isset($data['prefix_length'])) {
            $cidr = $data['network'] . '/' . $data['prefix_length'];
            $version = isset($data['ip_version']) ? (int)$data['ip_version'] : null;
            
            if (!self::validateCidr($cidr, $version)) {
                $errors['network'] = 'Invalid network address or prefix length.';
            }
        }
        
        // Validate gateway
        if (!empty($data['gateway'])) {
            $version = isset($data['ip_version']) ? (int)$data['ip_version'] : null;
            if (!self::validateIpAddress($data['gateway'], $version)) {
                $errors['gateway'] = 'Invalid gateway IP address.';
            }
        }
        
        // Validate VLAN ID
        if (!empty($data['vlan_id']) && !self::validateVlanId($data['vlan_id'])) {
            $errors['vlan_id'] = 'VLAN ID must be between 1 and 4094.';
        }
        
        return $errors;
    }
}
