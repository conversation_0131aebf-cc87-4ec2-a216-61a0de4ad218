<?php

namespace WHMCS\Module\Addon\Dcim\Models;

use WHMCS\Database\Capsule;

/**
 * Location Model Class
 * 
 * Handles data operations for DCIM locations
 */
class Location
{
    /**
     * Get all locations with optional filtering
     * 
     * @param array $filters Optional filters
     * @param string $orderBy Order by field
     * @param string $orderDir Order direction (asc/desc)
     * @param int $limit Limit results
     * @param int $offset Offset for pagination
     * @return array
     */
    public static function getAll($filters = [], $orderBy = 'name', $orderDir = 'asc', $limit = null, $offset = 0)
    {
        $query = Capsule::table('dcim_locations as dl')
            ->leftJoin('dcim_locations as parent', 'dl.parent_id', '=', 'parent.id')
            ->select([
                'dl.*',
                'parent.name as parent_name'
            ]);
        
        // Apply filters
        if (!empty($filters['type'])) {
            $query->where('dl.type', $filters['type']);
        }
        
        if (!empty($filters['status'])) {
            $query->where('dl.status', $filters['status']);
        }
        
        if (!empty($filters['search'])) {
            $search = '%' . $filters['search'] . '%';
            $query->where(function($q) use ($search) {
                $q->where('dl.name', 'like', $search)
                  ->orWhere('dl.city', 'like', $search)
                  ->orWhere('dl.address', 'like', $search);
            });
        }
        
        if (!empty($filters['parent_id'])) {
            $query->where('dl.parent_id', $filters['parent_id']);
        }
        
        // Apply ordering
        $query->orderBy('dl.' . $orderBy, $orderDir);
        
        // Apply pagination
        if ($limit) {
            $query->limit($limit)->offset($offset);
        }
        
        return $query->get()->toArray();
    }
    
    /**
     * Get location by ID
     * 
     * @param int $id Location ID
     * @return object|null
     */
    public static function getById($id)
    {
        return Capsule::table('dcim_locations as dl')
            ->leftJoin('dcim_locations as parent', 'dl.parent_id', '=', 'parent.id')
            ->select([
                'dl.*',
                'parent.name as parent_name'
            ])
            ->where('dl.id', $id)
            ->first();
    }
    
    /**
     * Create new location
     * 
     * @param array $data Location data
     * @return int|false Location ID or false on failure
     */
    public static function create($data)
    {
        try {
            $data['created_at'] = date('Y-m-d H:i:s');
            $data['updated_at'] = date('Y-m-d H:i:s');
            
            return Capsule::table('dcim_locations')->insertGetId($data);
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * Update location
     * 
     * @param int $id Location ID
     * @param array $data Updated data
     * @return bool Success status
     */
    public static function update($id, $data)
    {
        try {
            $data['updated_at'] = date('Y-m-d H:i:s');
            
            return Capsule::table('dcim_locations')
                ->where('id', $id)
                ->update($data) > 0;
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * Delete location
     * 
     * @param int $id Location ID
     * @return bool Success status
     */
    public static function delete($id)
    {
        try {
            // Check if location has children
            $hasChildren = Capsule::table('dcim_locations')
                ->where('parent_id', $id)
                ->exists();
            
            if ($hasChildren) {
                throw new \Exception('Cannot delete location with child locations');
            }
            
            // Check if location has racks
            $hasRacks = Capsule::table('dcim_racks')
                ->where('location_id', $id)
                ->exists();
            
            if ($hasRacks) {
                throw new \Exception('Cannot delete location with associated racks');
            }
            
            return Capsule::table('dcim_locations')
                ->where('id', $id)
                ->delete() > 0;
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * Get location hierarchy tree
     * 
     * @param int $parentId Parent location ID (null for root)
     * @return array Hierarchical array of locations
     */
    public static function getHierarchy($parentId = null)
    {
        $locations = Capsule::table('dcim_locations')
            ->where('parent_id', $parentId)
            ->where('status', 'active')
            ->orderBy('name')
            ->get()
            ->toArray();
        
        foreach ($locations as &$location) {
            $location->children = self::getHierarchy($location->id);
        }
        
        return $locations;
    }
    
    /**
     * Get location path (breadcrumb)
     * 
     * @param int $id Location ID
     * @return array Array of parent locations
     */
    public static function getPath($id)
    {
        $path = [];
        $location = self::getById($id);
        
        while ($location) {
            array_unshift($path, $location);
            $location = $location->parent_id ? self::getById($location->parent_id) : null;
        }
        
        return $path;
    }
    
    /**
     * Get locations for dropdown/select
     * 
     * @param string $type Location type filter
     * @param int $excludeId Exclude specific location ID
     * @return array
     */
    public static function getForSelect($type = null, $excludeId = null)
    {
        $query = Capsule::table('dcim_locations')
            ->select(['id', 'name', 'type', 'parent_id'])
            ->where('status', 'active')
            ->orderBy('name');
        
        if ($type) {
            $query->where('type', $type);
        }
        
        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }
        
        $locations = $query->get()->toArray();
        
        // Build hierarchical options
        return self::buildSelectOptions($locations);
    }
    
    /**
     * Build hierarchical select options
     * 
     * @param array $locations Flat array of locations
     * @param int $parentId Parent ID
     * @param string $prefix Indentation prefix
     * @return array
     */
    private static function buildSelectOptions($locations, $parentId = null, $prefix = '')
    {
        $options = [];
        
        foreach ($locations as $location) {
            if ($location->parent_id == $parentId) {
                $options[$location->id] = $prefix . $location->name . ' (' . ucfirst($location->type) . ')';
                
                // Add children recursively
                $children = self::buildSelectOptions($locations, $location->id, $prefix . '  ');
                $options = array_merge($options, $children);
            }
        }
        
        return $options;
    }
    
    /**
     * Get location statistics
     * 
     * @param int $id Location ID
     * @return array Statistics array
     */
    public static function getStatistics($id)
    {
        $stats = [
            'total_racks' => 0,
            'active_racks' => 0,
            'total_servers' => 0,
            'active_servers' => 0,
            'total_u_capacity' => 0,
            'used_u_capacity' => 0,
            'power_capacity' => 0,
            'power_usage' => 0,
        ];
        
        // Get location data
        $location = self::getById($id);
        if (!$location) {
            return $stats;
        }
        
        $stats['total_u_capacity'] = $location->total_u_capacity;
        $stats['power_capacity'] = $location->power_capacity_watts;
        
        // Get rack statistics
        $rackStats = Capsule::table('dcim_racks')
            ->where('location_id', $id)
            ->selectRaw('
                COUNT(*) as total_racks,
                SUM(CASE WHEN status = "active" THEN 1 ELSE 0 END) as active_racks
            ')
            ->first();
        
        if ($rackStats) {
            $stats['total_racks'] = $rackStats->total_racks;
            $stats['active_racks'] = $rackStats->active_racks;
        }
        
        // Get server statistics
        $serverStats = Capsule::table('dcim_servers as ds')
            ->join('dcim_racks as dr', 'ds.rack_id', '=', 'dr.id')
            ->where('dr.location_id', $id)
            ->selectRaw('
                COUNT(*) as total_servers,
                SUM(CASE WHEN ds.status = "active" THEN 1 ELSE 0 END) as active_servers,
                SUM(ds.u_size) as used_u_capacity,
                SUM(ds.power_consumption_watts) as power_usage
            ')
            ->first();
        
        if ($serverStats) {
            $stats['total_servers'] = $serverStats->total_servers;
            $stats['active_servers'] = $serverStats->active_servers;
            $stats['used_u_capacity'] = $serverStats->used_u_capacity ?: 0;
            $stats['power_usage'] = $serverStats->power_usage ?: 0;
        }
        
        return $stats;
    }
    
    /**
     * Get total count with filters
     * 
     * @param array $filters Optional filters
     * @return int Total count
     */
    public static function getCount($filters = [])
    {
        $query = Capsule::table('dcim_locations');
        
        // Apply filters
        if (!empty($filters['type'])) {
            $query->where('type', $filters['type']);
        }
        
        if (!empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }
        
        if (!empty($filters['search'])) {
            $search = '%' . $filters['search'] . '%';
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', $search)
                  ->orWhere('city', 'like', $search)
                  ->orWhere('address', 'like', $search);
            });
        }
        
        if (!empty($filters['parent_id'])) {
            $query->where('parent_id', $filters['parent_id']);
        }
        
        return $query->count();
    }
    
    /**
     * Check if location name is unique
     * 
     * @param string $name Location name
     * @param int $excludeId Exclude specific ID from check
     * @param int $parentId Parent location ID
     * @return bool
     */
    public static function isNameUnique($name, $excludeId = null, $parentId = null)
    {
        $query = Capsule::table('dcim_locations')
            ->where('name', $name)
            ->where('parent_id', $parentId);
        
        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }
        
        return !$query->exists();
    }
}
