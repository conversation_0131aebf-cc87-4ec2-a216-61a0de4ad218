<?php

namespace WHMCS\Module\Addon\Dcim\Services;

use WHMCS\Module\Addon\Dcim\Models\Location;
use WHMCS\Module\Addon\Dcim\Utils\ValidationHelper;
use WHMCS\Module\Addon\Dcim\Utils\PermissionHelper;

/**
 * Location Service Class
 * 
 * Provides business logic for location management
 */
class LocationService
{
    /**
     * Get paginated locations list
     * 
     * @param array $params Request parameters
     * @return array
     */
    public function getLocationsList($params = [])
    {
        $page = isset($params['page']) ? max(1, (int)$params['page']) : 1;
        $limit = isset($params['limit']) ? max(1, min(100, (int)$params['limit'])) : 25;
        $offset = ($page - 1) * $limit;
        
        $filters = [
            'type' => $params['type'] ?? null,
            'status' => $params['status'] ?? null,
            'search' => $params['search'] ?? null,
            'parent_id' => $params['parent_id'] ?? null,
        ];
        
        $orderBy = $params['order_by'] ?? 'name';
        $orderDir = $params['order_dir'] ?? 'asc';
        
        // Validate order parameters
        $allowedOrderBy = ['name', 'type', 'city', 'status', 'created_at'];
        if (!in_array($orderBy, $allowedOrderBy)) {
            $orderBy = 'name';
        }
        
        if (!in_array($orderDir, ['asc', 'desc'])) {
            $orderDir = 'asc';
        }
        
        $locations = Location::getAll($filters, $orderBy, $orderDir, $limit, $offset);
        $total = Location::getCount($filters);
        
        return [
            'locations' => $locations,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $limit,
                'total' => $total,
                'total_pages' => ceil($total / $limit),
                'has_next' => $page < ceil($total / $limit),
                'has_prev' => $page > 1,
            ]
        ];
    }
    
    /**
     * Get location by ID with validation
     * 
     * @param int $id Location ID
     * @return object|null
     * @throws \Exception If location not found
     */
    public function getLocation($id)
    {
        $location = Location::getById($id);
        
        if (!$location) {
            throw new \Exception('Location not found');
        }
        
        return $location;
    }
    
    /**
     * Create new location
     * 
     * @param array $data Location data
     * @return array Result with success status and location ID or errors
     */
    public function createLocation($data)
    {
        // Check permissions
        if (!PermissionHelper::hasPermission('dcim.locations.create')) {
            return [
                'success' => false,
                'errors' => ['permission' => 'Access denied. You do not have permission to create locations.']
            ];
        }
        
        // Validate input data
        $errors = ValidationHelper::validateLocationData($data);
        
        // Additional business logic validations
        if (empty($errors)) {
            // Check if name is unique within parent
            $parentId = !empty($data['parent_id']) ? $data['parent_id'] : null;
            if (!Location::isNameUnique($data['name'], null, $parentId)) {
                $errors['name'] = 'Location name must be unique within the same parent location.';
            }
            
            // Validate parent location exists and is valid
            if (!empty($data['parent_id'])) {
                $parent = Location::getById($data['parent_id']);
                if (!$parent) {
                    $errors['parent_id'] = 'Parent location not found.';
                } else {
                    // Validate hierarchy rules
                    $hierarchyError = $this->validateHierarchy($data['type'], $parent->type);
                    if ($hierarchyError) {
                        $errors['type'] = $hierarchyError;
                    }
                }
            }
        }
        
        if (!empty($errors)) {
            return [
                'success' => false,
                'errors' => $errors
            ];
        }
        
        // Sanitize data
        $sanitizedData = $this->sanitizeLocationData($data);
        
        // Create location
        $locationId = Location::create($sanitizedData);
        
        if ($locationId) {
            // Log action
            PermissionHelper::logAction('create', 'dcim_locations', $locationId, null, $sanitizedData);
            
            return [
                'success' => true,
                'location_id' => $locationId,
                'message' => 'Location created successfully.'
            ];
        } else {
            return [
                'success' => false,
                'errors' => ['general' => 'Failed to create location. Please try again.']
            ];
        }
    }
    
    /**
     * Update existing location
     * 
     * @param int $id Location ID
     * @param array $data Updated data
     * @return array Result with success status or errors
     */
    public function updateLocation($id, $data)
    {
        // Check permissions
        if (!PermissionHelper::hasPermission('dcim.locations.edit')) {
            return [
                'success' => false,
                'errors' => ['permission' => 'Access denied. You do not have permission to edit locations.']
            ];
        }
        
        // Get existing location
        $existingLocation = Location::getById($id);
        if (!$existingLocation) {
            return [
                'success' => false,
                'errors' => ['general' => 'Location not found.']
            ];
        }
        
        // Validate input data
        $errors = ValidationHelper::validateLocationData($data);
        
        // Additional business logic validations
        if (empty($errors)) {
            // Check if name is unique within parent
            $parentId = !empty($data['parent_id']) ? $data['parent_id'] : null;
            if (!Location::isNameUnique($data['name'], $id, $parentId)) {
                $errors['name'] = 'Location name must be unique within the same parent location.';
            }
            
            // Validate parent location
            if (!empty($data['parent_id'])) {
                // Cannot set self as parent
                if ($data['parent_id'] == $id) {
                    $errors['parent_id'] = 'Location cannot be its own parent.';
                } else {
                    $parent = Location::getById($data['parent_id']);
                    if (!$parent) {
                        $errors['parent_id'] = 'Parent location not found.';
                    } else {
                        // Check for circular reference
                        if ($this->wouldCreateCircularReference($id, $data['parent_id'])) {
                            $errors['parent_id'] = 'This would create a circular reference in the location hierarchy.';
                        }
                        
                        // Validate hierarchy rules
                        $hierarchyError = $this->validateHierarchy($data['type'], $parent->type);
                        if ($hierarchyError) {
                            $errors['type'] = $hierarchyError;
                        }
                    }
                }
            }
        }
        
        if (!empty($errors)) {
            return [
                'success' => false,
                'errors' => $errors
            ];
        }
        
        // Sanitize data
        $sanitizedData = $this->sanitizeLocationData($data);
        
        // Update location
        $success = Location::update($id, $sanitizedData);
        
        if ($success) {
            // Log action
            PermissionHelper::logAction('update', 'dcim_locations', $id, (array)$existingLocation, $sanitizedData);
            
            return [
                'success' => true,
                'message' => 'Location updated successfully.'
            ];
        } else {
            return [
                'success' => false,
                'errors' => ['general' => 'Failed to update location. Please try again.']
            ];
        }
    }
    
    /**
     * Delete location
     * 
     * @param int $id Location ID
     * @return array Result with success status or errors
     */
    public function deleteLocation($id)
    {
        // Check permissions
        if (!PermissionHelper::hasPermission('dcim.locations.delete')) {
            return [
                'success' => false,
                'errors' => ['permission' => 'Access denied. You do not have permission to delete locations.']
            ];
        }
        
        // Get existing location
        $location = Location::getById($id);
        if (!$location) {
            return [
                'success' => false,
                'errors' => ['general' => 'Location not found.']
            ];
        }
        
        // Delete location
        $success = Location::delete($id);
        
        if ($success) {
            // Log action
            PermissionHelper::logAction('delete', 'dcim_locations', $id, (array)$location, null);
            
            return [
                'success' => true,
                'message' => 'Location deleted successfully.'
            ];
        } else {
            return [
                'success' => false,
                'errors' => ['general' => 'Failed to delete location. It may have associated racks or child locations.']
            ];
        }
    }
    
    /**
     * Get location hierarchy tree
     * 
     * @return array Hierarchical tree structure
     */
    public function getLocationHierarchy()
    {
        return Location::getHierarchy();
    }
    
    /**
     * Get locations for dropdown select
     * 
     * @param string $type Location type filter
     * @param int $excludeId Exclude specific location
     * @return array
     */
    public function getLocationsForSelect($type = null, $excludeId = null)
    {
        return Location::getForSelect($type, $excludeId);
    }
    
    /**
     * Get location statistics
     * 
     * @param int $id Location ID
     * @return array Statistics
     */
    public function getLocationStatistics($id)
    {
        return Location::getStatistics($id);
    }
    
    /**
     * Validate location hierarchy rules
     * 
     * @param string $childType Child location type
     * @param string $parentType Parent location type
     * @return string|null Error message or null if valid
     */
    private function validateHierarchy($childType, $parentType)
    {
        $validHierarchy = [
            'region' => [],
            'facility' => ['region'],
            'floor' => ['facility'],
            'room' => ['facility', 'floor'],
        ];
        
        if (!isset($validHierarchy[$childType])) {
            return 'Invalid location type.';
        }
        
        $allowedParents = $validHierarchy[$childType];
        
        if (empty($allowedParents)) {
            // This type cannot have a parent
            return null;
        }
        
        if (!in_array($parentType, $allowedParents)) {
            return "A {$childType} cannot be a child of a {$parentType}.";
        }
        
        return null;
    }
    
    /**
     * Check if setting a parent would create a circular reference
     * 
     * @param int $locationId Location ID
     * @param int $parentId Proposed parent ID
     * @return bool
     */
    private function wouldCreateCircularReference($locationId, $parentId)
    {
        $currentId = $parentId;
        $visited = [];
        
        while ($currentId && !in_array($currentId, $visited)) {
            if ($currentId == $locationId) {
                return true;
            }
            
            $visited[] = $currentId;
            $location = Location::getById($currentId);
            $currentId = $location ? $location->parent_id : null;
        }
        
        return false;
    }
    
    /**
     * Sanitize location data
     * 
     * @param array $data Raw input data
     * @return array Sanitized data
     */
    private function sanitizeLocationData($data)
    {
        $sanitized = [];
        
        // String fields
        $stringFields = ['name', 'address', 'city', 'state', 'country', 'postal_code', 'contact_name', 'contact_email', 'contact_phone', 'notes'];
        foreach ($stringFields as $field) {
            if (isset($data[$field])) {
                $sanitized[$field] = ValidationHelper::sanitizeString($data[$field]);
            }
        }
        
        // Enum fields
        if (isset($data['type']) && in_array($data['type'], ['region', 'facility', 'floor', 'room'])) {
            $sanitized['type'] = $data['type'];
        }
        
        if (isset($data['status']) && in_array($data['status'], ['active', 'inactive', 'maintenance'])) {
            $sanitized['status'] = $data['status'];
        }
        
        // Numeric fields
        $numericFields = ['parent_id', 'total_u_capacity', 'power_capacity_watts', 'cooling_capacity_btu'];
        foreach ($numericFields as $field) {
            if (isset($data[$field]) && is_numeric($data[$field])) {
                $sanitized[$field] = (int)$data[$field];
            }
        }
        
        // Handle null parent_id
        if (isset($data['parent_id']) && empty($data['parent_id'])) {
            $sanitized['parent_id'] = null;
        }
        
        return $sanitized;
    }
}
