<?php
/**
 * Additional functions for managing blade servers
 * Add these functions to your main dcim.php file
 */

/**
 * Blade servers management page
 */
function dcim_blade_servers_page($modulelink)
{
    // Get blade servers with related data
    $bladeServers = Capsule::table('dcim_blade_servers as bs')
        ->leftJoin('dcim_chassis as c', 'bs.chassis_id', '=', 'c.id')
        ->leftJoin('dcim_blade_network as bn', 'bs.id', '=', 'bn.blade_server_id')
        ->leftJoin('dcim_switches as s', 'bn.switch_id', '=', 's.id')
        ->leftJoin('dcim_blade_ipmi as bi', 'bs.id', '=', 'bi.blade_server_id')
        ->select([
            'bs.*',
            'c.name as chassis_name',
            'c.model as chassis_model',
            's.name as switch_name',
            'bi.ipmi_address',
            'bi.mac_address'
        ])
        ->orderBy('bs.created_at', 'desc')
        ->get();

    // Get statistics
    $stats = [
        'total' => Capsule::table('dcim_blade_servers')->count(),
        'available' => Capsule::table('dcim_blade_servers')->where('status', 'available')->count(),
        'in_use' => Capsule::table('dcim_blade_servers')->where('status', 'in_use')->count(),
        'maintenance' => Capsule::table('dcim_blade_servers')->where('status', 'maintenance')->count(),
        'defect' => Capsule::table('dcim_blade_servers')->where('status', 'defect')->count(),
    ];

    return '
    <div class="dcim-modern-container">
        <div class="dcim-header">
            <h1 class="dcim-title">
                <i class="fas fa-microchip"></i>
                Blade Servers
            </h1>
            <div class="dcim-header-actions">
                <span class="dcim-last-updated">Last updated: ' . date('H:i') . '</span>
                <button class="dcim-btn dcim-btn-primary" onclick="openBulkAddModal()">
                    <i class="fas fa-plus"></i> Add Blade Servers
                </button>
            </div>
        </div>

        <div class="dcim-stats-grid">
            <div class="dcim-stat-card">
                <div class="dcim-stat-icon dcim-stat-primary">
                    <i class="fas fa-server"></i>
                </div>
                <div class="dcim-stat-content">
                    <div class="dcim-stat-number">' . $stats['total'] . '</div>
                    <div class="dcim-stat-label">Total Blade Servers</div>
                </div>
            </div>
            
            <div class="dcim-stat-card">
                <div class="dcim-stat-icon dcim-stat-success">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="dcim-stat-content">
                    <div class="dcim-stat-number">' . $stats['available'] . '</div>
                    <div class="dcim-stat-label">Available</div>
                </div>
            </div>
            
            <div class="dcim-stat-card">
                <div class="dcim-stat-icon dcim-stat-warning">
                    <i class="fas fa-cog"></i>
                </div>
                <div class="dcim-stat-content">
                    <div class="dcim-stat-number">' . $stats['in_use'] . '</div>
                    <div class="dcim-stat-label">In Use</div>
                </div>
            </div>
            
            <div class="dcim-stat-card">
                <div class="dcim-stat-icon dcim-stat-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="dcim-stat-content">
                    <div class="dcim-stat-number">' . $stats['defect'] . '</div>
                    <div class="dcim-stat-label">Defect</div>
                </div>
            </div>
        </div>

        <div class="dcim-filters-section">
            <div class="dcim-filter-group">
                <select class="dcim-filter" id="statusFilter">
                    <option value="">All Status</option>
                    <option value="available">Available</option>
                    <option value="in_use">In Use</option>
                    <option value="maintenance">Maintenance</option>
                    <option value="defect">Defect</option>
                </select>
                
                <select class="dcim-filter" id="chassisFilter">
                    <option value="">All Chassis</option>
                    ' . getChassisFilterOptions() . '
                </select>
                
                <select class="dcim-filter" id="cpuFilter">
                    <option value="">All CPUs</option>
                    ' . getCpuFilterOptions() . '
                </select>
            </div>
            
            <div class="dcim-search-box">
                <i class="fas fa-search"></i>
                <input type="text" placeholder="Search blade servers..." id="blade-search">
            </div>
        </div>

        <div class="dcim-blade-servers-table">
            <table class="dcim-table" id="bladeServersTable">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Label</th>
                        <th>CPU</th>
                        <th>RAM</th>
                        <th>Chassis</th>
                        <th>Switch</th>
                        <th>IPMI</th>
                        <th>MAC Address</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    ' . generateBladeServersTable($bladeServers) . '
                </tbody>
            </table>
        </div>
    </div>

    <script>
    function openBulkAddModal() {
        loadBulkAddModal();
    }

    function loadBulkAddModal() {
        $.get("' . $modulelink . '&action=bulk_add_blade_servers", function(data) {
            $("body").append(data);
            $("#bulkAddModal").modal("show");
        });
    }

    function editBladeServer(id) {
        // Implement edit functionality
        alert("Edit blade server ID: " + id);
    }

    function deleteBladeServer(id) {
        if (confirm("Are you sure you want to delete this blade server?")) {
            $.post("' . $modulelink . '&action=delete_blade_server", {id: id}, function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert("Error: " + response.message);
                }
            });
        }
    }

    function showBladeServerDetails(id) {
        // Implement details modal
        alert("Show details for blade server ID: " + id);
    }

    // Filter functionality
    $("#statusFilter, #chassisFilter, #cpuFilter").on("change", function() {
        filterBladeServers();
    });

    $("#blade-search").on("keyup", function() {
        filterBladeServers();
    });

    function filterBladeServers() {
        const statusFilter = $("#statusFilter").val();
        const chassisFilter = $("#chassisFilter").val();
        const cpuFilter = $("#cpuFilter").val();
        const searchTerm = $("#blade-search").val().toLowerCase();

        $("#bladeServersTable tbody tr").each(function() {
            const row = $(this);
            const status = row.find(".dcim-status").text().toLowerCase();
            const chassis = row.find("td:nth-child(5)").text().toLowerCase();
            const cpu = row.find("td:nth-child(3)").text().toLowerCase();
            const label = row.find("td:nth-child(2)").text().toLowerCase();

            let show = true;

            if (statusFilter && !status.includes(statusFilter)) show = false;
            if (chassisFilter && !chassis.includes(chassisFilter.toLowerCase())) show = false;
            if (cpuFilter && !cpu.includes(cpuFilter.toLowerCase())) show = false;
            if (searchTerm && !label.includes(searchTerm) && !cpu.includes(searchTerm)) show = false;

            row.toggle(show);
        });
    }
    </script>';
}

/**
 * Generate blade servers table
 */
function generateBladeServersTable($bladeServers)
{
    $html = '';
    
    if ($bladeServers->isEmpty()) {
        return '<tr><td colspan="10" class="text-center">No blade servers found. <a href="#" onclick="openBulkAddModal()">Add some blade servers</a></td></tr>';
    }

    foreach ($bladeServers as $server) {
        $statusClass = getBladeStatusClass($server->status);
        $statusText = getBladeStatusText($server->status);
        
        $html .= '
        <tr class="dcim-table-row" onclick="showBladeServerDetails(' . $server->id . ')">
            <td>#' . $server->id . '</td>
            <td><strong>' . htmlspecialchars($server->label) . '</strong></td>
            <td>' . htmlspecialchars($server->cpu_model ?: 'Not specified') . '</td>
            <td>' . htmlspecialchars($server->ram_config ?: 'Not specified') . '</td>
            <td>' . htmlspecialchars($server->chassis_name ?: 'Not assigned') . '</td>
            <td>' . htmlspecialchars($server->switch_name ?: 'Not connected') . '</td>
            <td>' . htmlspecialchars($server->ipmi_address ?: 'Not configured') . '</td>
            <td>' . htmlspecialchars($server->mac_address ?: 'Not detected') . '</td>
            <td>
                <span class="dcim-status dcim-status-' . $statusClass . '">
                    <i class="fas fa-circle"></i>
                    ' . $statusText . '
                </span>
            </td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="dcim-btn-icon" onclick="event.stopPropagation(); editBladeServer(' . $server->id . ')" title="Edit">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="dcim-btn-icon dcim-btn-danger" onclick="event.stopPropagation(); deleteBladeServer(' . $server->id . ')" title="Delete">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>';
    }
    
    return $html;
}

/**
 * Get blade server status class
 */
function getBladeStatusClass($status)
{
    switch ($status) {
        case 'available': return 'success';
        case 'in_use': return 'warning';
        case 'maintenance': return 'info';
        case 'defect': return 'danger';
        default: return 'secondary';
    }
}

/**
 * Get blade server status text
 */
function getBladeStatusText($status)
{
    switch ($status) {
        case 'available': return 'Available';
        case 'in_use': return 'In Use';
        case 'maintenance': return 'Maintenance';
        case 'defect': return 'Defect';
        default: return 'Unknown';
    }
}

/**
 * Get chassis filter options
 */
function getChassisFilterOptions()
{
    try {
        $chassis = Capsule::table('dcim_chassis')
            ->select('name')
            ->distinct()
            ->orderBy('name')
            ->get();
        
        $options = '';
        foreach ($chassis as $item) {
            $options .= '<option value="' . htmlspecialchars($item->name) . '">' . htmlspecialchars($item->name) . '</option>';
        }
        
        return $options;
    } catch (\Exception $e) {
        return '';
    }
}

/**
 * Get CPU filter options
 */
function getCpuFilterOptions()
{
    try {
        $cpus = Capsule::table('dcim_blade_servers')
            ->select('cpu_model')
            ->distinct()
            ->whereNotNull('cpu_model')
            ->orderBy('cpu_model')
            ->get();
        
        $options = '';
        foreach ($cpus as $cpu) {
            $options .= '<option value="' . htmlspecialchars($cpu->cpu_model) . '">' . htmlspecialchars($cpu->cpu_model) . '</option>';
        }
        
        return $options;
    } catch (\Exception $e) {
        return '';
    }
}

/**
 * Delete blade server
 */
function dcim_delete_blade_server($modulelink)
{
    header('Content-Type: application/json');
    
    try {
        $id = (int)($_POST['id'] ?? 0);
        
        if (!$id) {
            return json_encode(['success' => false, 'message' => 'Invalid server ID']);
        }
        
        // Check if server exists
        $server = Capsule::table('dcim_blade_servers')->where('id', $id)->first();
        if (!$server) {
            return json_encode(['success' => false, 'message' => 'Server not found']);
        }
        
        // Delete related records first (cascade should handle this, but being explicit)
        Capsule::table('dcim_blade_network')->where('blade_server_id', $id)->delete();
        Capsule::table('dcim_blade_ipmi')->where('blade_server_id', $id)->delete();
        
        // Delete the server
        Capsule::table('dcim_blade_servers')->where('id', $id)->delete();
        
        return json_encode(['success' => true, 'message' => 'Blade server deleted successfully']);
        
    } catch (\Exception $e) {
        return json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

/**
 * Update blade server status
 */
function dcim_update_blade_server_status($modulelink)
{
    header('Content-Type: application/json');
    
    try {
        $id = (int)($_POST['id'] ?? 0);
        $status = $_POST['status'] ?? '';
        
        if (!$id || !$status) {
            return json_encode(['success' => false, 'message' => 'Invalid parameters']);
        }
        
        $validStatuses = ['available', 'in_use', 'maintenance', 'defect'];
        if (!in_array($status, $validStatuses)) {
            return json_encode(['success' => false, 'message' => 'Invalid status']);
        }
        
        $updated = Capsule::table('dcim_blade_servers')
            ->where('id', $id)
            ->update([
                'status' => $status,
                'updated_at' => date('Y-m-d H:i:s')
            ]);
        
        if ($updated) {
            return json_encode(['success' => true, 'message' => 'Status updated successfully']);
        } else {
            return json_encode(['success' => false, 'message' => 'Server not found']);
        }
        
    } catch (\Exception $e) {
        return json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

/**
 * Get blade server details
 */
function dcim_get_blade_server_details($modulelink)
{
    header('Content-Type: application/json');
    
    try {
        $id = (int)($_GET['id'] ?? 0);
        
        if (!$id) {
            return json_encode(['success' => false, 'message' => 'Invalid server ID']);
        }
        
        $server = Capsule::table('dcim_blade_servers as bs')
            ->leftJoin('dcim_chassis as c', 'bs.chassis_id', '=', 'c.id')
            ->leftJoin('dcim_blade_network as bn', 'bs.id', '=', 'bn.blade_server_id')
            ->leftJoin('dcim_switches as s', 'bn.switch_id', '=', 's.id')
            ->leftJoin('dcim_blade_ipmi as bi', 'bs.id', '=', 'bi.blade_server_id')
            ->select([
                'bs.*',
                'c.name as chassis_name',
                'c.model as chassis_model',
                'c.blade_slots',
                's.name as switch_name',
                's.model as switch_model',
                'bn.ports as network_ports',
                'bi.ipmi_address',
                'bi.ipmi_password',
                'bi.mac_address'
            ])
            ->where('bs.id', $id)
            ->first();
        
        if (!$server) {
            return json_encode(['success' => false, 'message' => 'Server not found']);
        }
        
        return json_encode(['success' => true, 'server' => $server]);
        
    } catch (\Exception $e) {
        return json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

/**
 * Export blade servers to CSV
 */
function dcim_export_blade_servers_csv($modulelink)
{
    try {
        $servers = Capsule::table('dcim_blade_servers as bs')
            ->leftJoin('dcim_chassis as c', 'bs.chassis_id', '=', 'c.id')
            ->leftJoin('dcim_blade_network as bn', 'bs.id', '=', 'bn.blade_server_id')
            ->leftJoin('dcim_switches as s', 'bn.switch_id', '=', 's.id')
            ->leftJoin('dcim_blade_ipmi as bi', 'bs.id', '=', 'bi.blade_server_id')
            ->select([
                'bs.id',
                'bs.label',
                'bs.cpu_model',
                'bs.ram_config',
                'bs.status',
                'c.name as chassis_name',
                's.name as switch_name',
                'bi.ipmi_address',
                'bi.mac_address',
                'bs.created_at'
            ])
            ->get();
        
        $filename = 'blade_servers_' . date('Y-m-d_H-i-s') . '.csv';
        
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        $output = fopen('php://output', 'w');
        
        // CSV headers
        fputcsv($output, [
            'ID',
            'Label',
            'CPU Model',
            'RAM Config',
            'Status',
            'Chassis',
            'Switch',
            'IPMI Address',
            'MAC Address',
            'Created At'
        ]);
        
        // Data rows
        foreach ($servers as $server) {
            fputcsv($output, [
                $server->id,
                $server->label,
                $server->cpu_model,
                $server->ram_config,
                $server->status,
                $server->chassis_name,
                $server->switch_name,
                $server->ipmi_address,
                $server->mac_address,
                $server->created_at
            ]);
        }
        
        fclose($output);
        
    } catch (\Exception $e) {
        echo 'Error generating CSV: ' . $e->getMessage();
    }
}