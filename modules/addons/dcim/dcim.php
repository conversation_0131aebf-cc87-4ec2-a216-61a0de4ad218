<?php
/**
 * <PERSON><PERSON> (Data Center Infrastructure Management) <PERSON>don Module for WHMCS
 * Modern Design with Bulk Add Blade Servers
 * 
 * @package    WHMCS
 * <AUTHOR> Company Name
 * @copyright  Copyright (c) 2025 Your Company Name
 * @license    MIT License
 * @version    1.0.0
 */

use WHMCS\Database\Capsule;

if (!defined("WHMCS")) {
    die("This file cannot be accessed directly");
}

/**
 * Module configuration array
 */
function dcim_config()
{
    return [
        'name' => 'DCIM - Data Center Infrastructure Management',
        'description' => 'Modern data center infrastructure management with locations, racks, servers, and inventory tracking.',
        'author' => 'Your Company Name',
        'language' => 'english',
        'version' => '1.0.0',
        'fields' => [
            'enable_audit_logging' => [
                'FriendlyName' => 'Enable Audit Logging',
                'Type' => 'yesno',
                'Description' => 'Enable comprehensive audit logging for all DCIM operations',
                'Default' => 'on',
            ],
            'default_rack_height' => [
                'FriendlyName' => 'Default Rack Height (U)',
                'Type' => 'text',
                'Size' => '10',
                'Default' => '42',
                'Description' => 'Default height in U for new racks',
            ],
            'enable_notifications' => [
                'FriendlyName' => 'Enable Notifications',
                'Type' => 'yesno',
                'Description' => 'Enable system notifications for status changes',
                'Default' => 'on',
            ],
        ]
    ];
}

/**
 * Module activation function
 */
function dcim_activate()
{
    try {
        $result = createDcimTables();
        
        if (!$result['success']) {
            return [
                'status' => 'error',
                'description' => $result['message']
            ];
        }
        
        // Create sample data
        createSampleData();
        
        return [
            'status' => 'success',
            'description' => 'DCIM module activated successfully! You can now access it from the Addons menu.'
        ];
        
    } catch (\Exception $e) {
        return [
            'status' => 'error',
            'description' => 'Failed to activate DCIM module: ' . $e->getMessage()
        ];
    }
}

/**
 * Module deactivation function
 */
function dcim_deactivate()
{
    return [
        'status' => 'success',
        'description' => 'DCIM module deactivated successfully.'
    ];
}

/**
 * Admin area output function
 */
function dcim_output($vars)
{
    $modulelink = $vars['modulelink'];
    $action = $_REQUEST['action'] ?? 'dashboard';

    try {
        if (!checkDcimTablesExist()) {
            echo dcim_database_setup_page($modulelink);
            return;
        }

        switch ($action) {
            case 'dashboard':
            case '':
                echo dcim_modern_dashboard($modulelink);
                break;
                
            case 'locations':
                echo dcim_locations_page($modulelink);
                break;
                
            case 'inventory':
                echo dcim_inventory_page($modulelink);
                break;
                
            case 'bulk_add_blade_servers':
                echo dcim_bulk_add_blade_servers_modal($modulelink);
                break;
                
            case 'process_bulk_add':
                echo dcim_process_bulk_add($modulelink);
                break;
                
            case 'get_chassis_list':
                echo dcim_get_chassis_list();
                break;
                
            case 'get_switch_list':
                echo dcim_get_switch_list();
                break;
                
            case 'get_cpu_models':
                echo dcim_get_cpu_models();
                break;
                
                            case 'get_ram_configs':
                echo dcim_get_ram_configs();
                break;
                
            case 'add_cpu_model':
                echo dcim_add_cpu_model();
                break;
                
            case 'add_ram_config':
                echo dcim_add_ram_config();
                break;
                
                            case 'manage_hardware_models':
                echo dcim_manage_hardware_models($modulelink);
                break;
                
            case 'delete_cpu_model':
                echo dcim_delete_cpu_model();
                break;
                
            case 'delete_ram_config':
                echo dcim_delete_ram_config();
                break;
                
            case 'delete_blade_server':
                echo dcim_delete_blade_server();
                break;
                
            case 'blade_servers':
                echo dcim_blade_servers_page($modulelink);
                break;
                
            case 'ajax_stats':
                echo dcim_ajax_stats();
                break;
                
            default:
                echo dcim_modern_dashboard($modulelink);
        }

    } catch (\Exception $e) {
        echo dcim_error_page($e->getMessage());
    }
}

/**
 * Modern dashboard page
 */
function dcim_modern_dashboard($modulelink)
{
    return '
    <div class="dcim-modern-container">
        <div class="dcim-header">
            <h1 class="dcim-title">
                <i class="fas fa-server"></i>
                DCIM Dashboard
            </h1>
            <p class="dcim-subtitle">Data Center Infrastructure Management</p>
        </div>

        <div class="dcim-stats-grid">
            <div class="dcim-stat-card">
                <div class="dcim-stat-icon dcim-stat-primary">
                    <i class="fas fa-building"></i>
                </div>
                <div class="dcim-stat-content">
                    <div class="dcim-stat-number" id="stat-locations">...</div>
                    <div class="dcim-stat-label">Total Locations</div>
                </div>
            </div>
            
            <div class="dcim-stat-card">
                <div class="dcim-stat-icon dcim-stat-success">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="dcim-stat-content">
                    <div class="dcim-stat-number" id="stat-available">...</div>
                    <div class="dcim-stat-label">Available Servers</div>
                </div>
            </div>
            
            <div class="dcim-stat-card">
                <div class="dcim-stat-icon dcim-stat-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="dcim-stat-content">
                    <div class="dcim-stat-number" id="stat-defect">...</div>
                    <div class="dcim-stat-label">Defect</div>
                </div>
            </div>
            
            <div class="dcim-stat-card">
                <div class="dcim-stat-icon dcim-stat-info">
                    <i class="fas fa-server"></i>
                </div>
                <div class="dcim-stat-content">
                    <div class="dcim-stat-number" id="stat-total">...</div>
                    <div class="dcim-stat-label">Total Servers</div>
                </div>
            </div>
        </div>

        <div class="dcim-quick-actions">
            <div class="dcim-action-grid">
                <a href="' . $modulelink . '&action=locations" class="dcim-action-card">
                    <div class="dcim-action-icon">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <h3>Locations</h3>
                    <p>Manage data center locations and facilities</p>
                </a>
                
                <a href="' . $modulelink . '&action=inventory" class="dcim-action-card">
                    <div class="dcim-action-icon">
                        <i class="fas fa-server"></i>
                    </div>
                    <h3>Inventory Management</h3>
                    <p>Track and manage server inventory</p>
                </a>
                
                <a href="#" onclick="openBulkAddModal()" class="dcim-action-card">
                    <div class="dcim-action-icon">
                        <i class="fas fa-plus-circle"></i>
                    </div>
                    <h3>Bulk Add Blade Servers</h3>
                    <p>Add multiple blade servers at once</p>
                </a>
                
                <a href="' . $modulelink . '&action=manage_hardware_models" class="dcim-action-card">
                    <div class="dcim-action-icon">
                        <i class="fas fa-microchip"></i>
                    </div>
                    <h3>Hardware Models</h3>
                    <p>Manage CPU and RAM configurations</p>
                </a>
            </div>
        </div>
    </div>

    <script>
    $(document).ready(function() {
        loadDashboardStats();
        setInterval(loadDashboardStats, 30000);
    });

    function loadDashboardStats() {
        $.get("' . $modulelink . '&action=ajax_stats", function(data) {
            if (data.success) {
                $("#stat-locations").text(data.stats.locations);
                $("#stat-available").text(data.stats.available_servers);
                $("#stat-defect").text(data.stats.defect_servers);
                $("#stat-total").text(data.stats.total_servers);
            }
        });
    }

    function openBulkAddModal() {
        loadBulkAddModal();
    }

    function loadBulkAddModal() {
        $.get("' . $modulelink . '&action=bulk_add_blade_servers", function(data) {
            $("body").append(data);
            $("#bulkAddModal").modal("show");
        });
    }
    </script>';
}

/**
 * Inventory page with bulk add functionality
 */
function dcim_inventory_page($modulelink)
{
    // Get server statistics
    $totalServers = Capsule::table('dcim_servers')->count();
    $availableServers = Capsule::table('dcim_servers')->where('status', 'active')->count();
    $inUseServers = Capsule::table('dcim_servers')->where('status', 'provisioning')->count();
    $defectServers = Capsule::table('dcim_servers')->where('status', 'failed')->count();

    // Get servers with location info
    $servers = Capsule::table('dcim_servers as s')
        ->leftJoin('dcim_racks as r', 's.rack_id', '=', 'r.id')
        ->leftJoin('dcim_locations as l', 'r.location_id', '=', 'l.id')
        ->select([
            's.*',
            'r.name as rack_name',
            'l.name as location_name',
            'l.city as location_city',
            'l.country as location_country'
        ])
        ->limit(20)
        ->get();

    return '
    <div class="dcim-modern-container">
        <div class="dcim-header">
            <h1 class="dcim-title">
                <i class="fas fa-boxes"></i>
                Inventory Management
            </h1>
            <div class="dcim-header-actions">
                <span class="dcim-last-updated">Last updated: ' . date('H:i') . '</span>
                <button class="dcim-btn dcim-btn-primary" onclick="openBulkAddModal()">
                    <i class="fas fa-plus"></i> Bulk Add
                </button>
            </div>
        </div>

        <div class="dcim-stats-grid">
            <div class="dcim-stat-card">
                <div class="dcim-stat-icon dcim-stat-primary">
                    <i class="fas fa-server"></i>
                </div>
                <div class="dcim-stat-content">
                    <div class="dcim-stat-number">' . $totalServers . '</div>
                    <div class="dcim-stat-label">Total Servers</div>
                </div>
            </div>
            
            <div class="dcim-stat-card">
                <div class="dcim-stat-icon dcim-stat-success">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="dcim-stat-content">
                    <div class="dcim-stat-number">' . $availableServers . '</div>
                    <div class="dcim-stat-label">Available Servers</div>
                </div>
            </div>
            
            <div class="dcim-stat-card">
                <div class="dcim-stat-icon dcim-stat-warning">
                    <i class="fas fa-cog"></i>
                </div>
                <div class="dcim-stat-content">
                    <div class="dcim-stat-number">' . $inUseServers . '</div>
                    <div class="dcim-stat-label">In use</div>
                </div>
            </div>
            
            <div class="dcim-stat-card">
                <div class="dcim-stat-icon dcim-stat-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="dcim-stat-content">
                    <div class="dcim-stat-number">' . $defectServers . '</div>
                    <div class="dcim-stat-label">Defect</div>
                </div>
            </div>
        </div>

        <div class="dcim-inventory-tabs">
            <div class="dcim-tabs">
                <button class="dcim-tab dcim-tab-active" data-tab="servers">
                    <i class="fas fa-server"></i> Dedicated Servers
                </button>
                <button class="dcim-tab" data-tab="blade">
                    <i class="fas fa-microchip"></i> Blade Servers
                </button>
                <button class="dcim-tab" data-tab="chassis">
                    <i class="fas fa-hdd"></i> Chassis
                </button>
                <button class="dcim-tab" data-tab="switches">
                    <i class="fas fa-network-wired"></i> Switches
                </button>
            </div>
        </div>

        <div class="dcim-inventory-table">
            <table class="dcim-table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>LABEL</th>
                        <th>CPU</th>
                        <th>LOCATION</th>
                        <th>RACK</th>
                        <th>STORAGE</th>
                        <th>PORTS</th>
                        <th>ORDER</th>
                        <th>STATUS</th>
                        <th>ACTIONS</th>
                    </tr>
                </thead>
                <tbody>
                    ' . generateInventoryTable($servers) . '
                </tbody>
            </table>
        </div>
    </div>

    <script>
    function openBulkAddModal() {
        loadBulkAddModal();
    }

    function loadBulkAddModal() {
        $.get("' . $modulelink . '&action=bulk_add_blade_servers", function(data) {
            $("body").append(data);
            $("#bulkAddModal").modal("show");
        });
    }
    </script>';
}

/**
 * Bulk Add Blade Servers Modal
 */
function dcim_bulk_add_blade_servers_modal($modulelink)
{
    return '
    <div class="modal fade" id="bulkAddModal" tabindex="-1" role="dialog" aria-labelledby="bulkAddModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="bulkAddModalLabel">
                        <i class="fas fa-plus-circle"></i> Bulk Add Blade Servers
                    </h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="bulkAddForm">
                        <!-- Tab Navigation -->
                        <ul class="nav nav-tabs" id="bulkAddTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <a class="nav-link active" id="basic-tab" data-toggle="tab" href="#basic" role="tab">
                                    <i class="fas fa-info-circle"></i> Basic Info
                                </a>
                            </li>
                            <li class="nav-item" role="presentation">
                                <a class="nav-link" id="network-tab" data-toggle="tab" href="#network" role="tab">
                                    <i class="fas fa-network-wired"></i> Network
                                </a>
                            </li>
                            <li class="nav-item" role="presentation">
                                <a class="nav-link" id="chassis-tab" data-toggle="tab" href="#chassis" role="tab">
                                    <i class="fas fa-hdd"></i> Chassis
                                </a>
                            </li>
                            <li class="nav-item" role="presentation">
                                <a class="nav-link" id="ipmi-tab" data-toggle="tab" href="#ipmi" role="tab">
                                    <i class="fas fa-microchip"></i> IPMI
                                </a>
                            </li>
                        </ul>

                        <!-- Tab Content -->
                        <div class="tab-content" id="bulkAddTabContent">
                            <!-- Basic Info Tab -->
                            <div class="tab-pane fade show active" id="basic" role="tabpanel">
                                <div class="dcim-bulk-add-header">
                                    <div class="dcim-bulk-actions">
                                        <button type="button" class="dcim-btn dcim-btn-primary" onclick="addRow()">
                                            <i class="fas fa-plus"></i> Add Row
                                        </button>
                                        <button type="button" class="dcim-btn dcim-btn-secondary" onclick="add5Rows()">
                                            <i class="fas fa-plus"></i> Add 5 Rows
                                        </button>
                                        <button type="button" class="dcim-btn dcim-btn-outline" onclick="generateSequentialNames()">
                                            Generate Sequential Names
                                        </button>
                                    </div>
                                </div>

                                <div class="dcim-bulk-table">
                                    <table class="table table-striped" id="basicInfoTable">
                                        <thead>
                                            <tr>
                                                <th width="5%">#</th>
                                                <th width="25%">Label*</th>
                                                <th width="25%">CPU*</th>
                                                <th width="20%">RAM</th>
                                                <th width="15%">Status</th>
                                                <th width="10%">Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody id="basicInfoBody">
                                            <!-- Dynamic rows will be added here -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- Network Tab -->
                            <div class="tab-pane fade" id="network" role="tabpanel">
                                <div class="dcim-bulk-add-header">
                                    <div class="dcim-bulk-actions">
                                        <button type="button" class="dcim-btn dcim-btn-primary" onclick="addNetworkRow()">
                                            <i class="fas fa-plus"></i> Add Row
                                        </button>
                                        <button type="button" class="dcim-btn dcim-btn-secondary" onclick="add5NetworkRows()">
                                            <i class="fas fa-plus"></i> Add 5 Rows
                                        </button>
                                    </div>
                                </div>

                                <div class="dcim-bulk-table">
                                    <table class="table table-striped" id="networkTable">
                                        <thead>
                                            <tr>
                                                <th width="5%">#</th>
                                                <th width="20%">Label</th>
                                                <th width="40%">Switch</th>
                                                <th width="25%">Ports</th>
                                                <th width="10%">Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody id="networkBody">
                                            <!-- Dynamic rows will be added here -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- Chassis Tab -->
                            <div class="tab-pane fade" id="chassis" role="tabpanel">
                                <div class="dcim-bulk-add-header">
                                    <div class="dcim-bulk-actions">
                                        <button type="button" class="dcim-btn dcim-btn-primary" onclick="addChassisRow()">
                                            <i class="fas fa-plus"></i> Add Row
                                        </button>
                                        <button type="button" class="dcim-btn dcim-btn-secondary" onclick="add5ChassisRows()">
                                            <i class="fas fa-plus"></i> Add 5 Rows
                                        </button>
                                    </div>
                                </div>

                                <div class="dcim-bulk-table">
                                    <table class="table table-striped" id="chassisTable">
                                        <thead>
                                            <tr>
                                                <th width="5%">#</th>
                                                <th width="25%">Label</th>
                                                <th width="60%">Chassis*</th>
                                                <th width="10%">Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody id="chassisBody">
                                            <!-- Dynamic rows will be added here -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- IPMI Tab -->
                            <div class="tab-pane fade" id="ipmi" role="tabpanel">
                                <div class="dcim-bulk-add-header">
                                    <div class="dcim-bulk-actions">
                                        <button type="button" class="dcim-btn dcim-btn-primary" onclick="addIpmiRow()">
                                            <i class="fas fa-plus"></i> Add Row
                                        </button>
                                        <button type="button" class="dcim-btn dcim-btn-secondary" onclick="add5IpmiRows()">
                                            <i class="fas fa-plus"></i> Add 5 Rows
                                        </button>
                                        <button type="button" class="dcim-btn dcim-btn-outline" onclick="detectMacAddresses()">
                                            <i class="fas fa-wifi"></i> Detect MAC Addresses
                                        </button>
                                    </div>
                                </div>

                                <div class="dcim-bulk-table">
                                    <table class="table table-striped" id="ipmiTable">
                                        <thead>
                                            <tr>
                                                <th width="5%">#</th>
                                                <th width="15%">Label</th>
                                                <th width="25%">IPMI Address</th>
                                                <th width="20%">IPMI Root Password</th>
                                                <th width="25%">MAC Address</th>
                                                <th width="10%">Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody id="ipmiBody">
                                            <!-- Dynamic rows will be added here -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </form>

                    <div class="dcim-bulk-summary">
                        <span id="serverCount">0</span> Blade Servers configured
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="submitBulkAdd()">
                        <i class="fas fa-plus"></i> Add <span id="addButtonCount">0</span> Blade Servers
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
    let rowCounter = 0;
    let cpuModels = [];
    let ramConfigs = [];
    let chassisList = [];
    let switchList = [];

    $(document).ready(function() {
        loadFormData();
        addRow(); // Add initial row
        updateServerCount();
    });

    function loadFormData() {
        // Load CPU models
        $.get("' . $modulelink . '&action=get_cpu_models", function(data) {
            cpuModels = data;
        });

        // Load RAM configurations
        $.get("' . $modulelink . '&action=get_ram_configs", function(data) {
            ramConfigs = data;
        });

        // Load chassis list
        $.get("' . $modulelink . '&action=get_chassis_list", function(data) {
            chassisList = data;
        });

        // Load switch list
        $.get("' . $modulelink . '&action=get_switch_list", function(data) {
            switchList = data;
        });
    }

    function addRow() {
        rowCounter++;
        const row = `
            <tr id="row-${rowCounter}">
                <td>${rowCounter}</td>
                <td><input type="text" class="form-control" name="basic[${rowCounter}][label]" placeholder="Blade Server Label" required></td>
                <td>
                    <select class="form-control" name="basic[${rowCounter}][cpu]" required>
                        <option value="">Select CPU Model</option>
                        ${getCpuOptions()}
                    </select>
                </td>
                <td>
                    <select class="form-control" name="basic[${rowCounter}][ram]">
                        <option value="">Select RAM Configuration</option>
                        ${getRamOptions()}
                    </select>
                </td>
                <td>
                    <select class="form-control" name="basic[${rowCounter}][status]">
                        <option value="Available">Available</option>
                        <option value="In Use">In Use</option>
                        <option value="Maintenance">Maintenance</option>
                        <option value="Defect">Defect</option>
                    </select>
                </td>
                <td>
                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="duplicateRow(${rowCounter})">
                        <i class="fas fa-copy"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeRow(${rowCounter})">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `;
        $("#basicInfoBody").append(row);
        addNetworkRow();
        addChassisRow();
        addIpmiRow();
        updateServerCount();
    }

    function add5Rows() {
        for (let i = 0; i < 5; i++) {
            addRow();
        }
    }

    function addNetworkRow() {
        const row = `
            <tr id="network-row-${rowCounter}">
                <td>${rowCounter}</td>
                <td>Device ${rowCounter}</td>
                <td>
                    <select class="form-control" name="network[${rowCounter}][switch]">
                        <option value="">Select Switch</option>
                        ${getSwitchOptions()}
                    </select>
                </td>
                <td>
                    <input type="text" class="form-control" name="network[${rowCounter}][ports]" placeholder="Select a switch first" disabled>
                </td>
                <td>
                    <button type="button" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-link"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-copy"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeNetworkRow(${rowCounter})">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `;
        $("#networkBody").append(row);
    }

    function addChassisRow() {
        const row = `
            <tr id="chassis-row-${rowCounter}">
                <td>${rowCounter}</td>
                <td>Server ${rowCounter}</td>
                <td>
                    <select class="form-control" name="chassis[${rowCounter}][chassis]" required>
                        <option value="">Select Chassis</option>
                        ${getChassisOptions()}
                    </select>
                </td>
                <td>
                    <button type="button" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-copy"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeChassisRow(${rowCounter})">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `;
        $("#chassisBody").append(row);
    }

    function addIpmiRow() {
        const row = `
            <tr id="ipmi-row-${rowCounter}">
                <td>${rowCounter}</td>
                <td>Device ${rowCounter}</td>
                <td>
                    <div class="input-group">
                        <select class="form-control" name="ipmi[${rowCounter}][address]">
                            <option value="">IPMI address</option>
                        </select>
                        <div class="input-group-append">
                            <button type="button" class="btn btn-outline-primary">
                                <i class="fas fa-sync"></i>
                            </button>
                        </div>
                    </div>
                    <small class="text-warning">Select a city first</small>
                </td>
                <td>
                    <div class="input-group">
                        <input type="password" class="form-control" name="ipmi[${rowCounter}][password]" placeholder="Root Password">
                        <div class="input-group-append">
                            <button type="button" class="btn btn-outline-secondary" onclick="togglePassword(this)">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                </td>
                <td>
                    <input type="text" class="form-control" name="ipmi[${rowCounter}][mac]" placeholder="MAC Address">
                </td>
                <td>
                    <button type="button" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-copy"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeIpmiRow(${rowCounter})">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `;
        $("#ipmiBody").append(row);
    }

    function removeRow(id) {
        $("#row-" + id).remove();
        $("#network-row-" + id).remove();
        $("#chassis-row-" + id).remove();
        $("#ipmi-row-" + id).remove();
        updateServerCount();
    }

    function duplicateRow(id) {
        const originalRow = $("#row-" + id);
        addRow();
        // Copy values from original row to new row
        const newRowId = rowCounter;
        $("#row-" + newRowId + " input").each(function(index) {
            $(this).val(originalRow.find("input").eq(index).val());
        });
        $("#row-" + newRowId + " select").each(function(index) {
            $(this).val(originalRow.find("select").eq(index).val());
        });
    }

    function generateSequentialNames() {
        $("#basicInfoBody tr").each(function(index) {
            const input = $(this).find("input[name*=\'[label]\']");
            input.val("Blade Server " + (index + 1));
        });
    }

    function detectMacAddresses() {
        // Simulate MAC address detection
        $("#ipmiBody tr").each(function() {
            const macInput = $(this).find("input[name*=\'[mac]\']");
            if (!macInput.val()) {
                macInput.val(generateRandomMac());
            }
        });
    }

    function generateRandomMac() {
        const chars = "0123456789ABCDEF";
        let mac = "";
        for (let i = 0; i < 6; i++) {
            if (i > 0) mac += ":";
            mac += chars.charAt(Math.floor(Math.random() * 16));
            mac += chars.charAt(Math.floor(Math.random() * 16));
        }
        return mac;
    }

    function togglePassword(btn) {
        const input = $(btn).closest(".input-group").find("input");
        const icon = $(btn).find("i");
        if (input.attr("type") === "password") {
            input.attr("type", "text");
            icon.removeClass("fa-eye").addClass("fa-eye-slash");
        } else {
            input.attr("type", "password");
            icon.removeClass("fa-eye-slash").addClass("fa-eye");
        }
    }

    function updateServerCount() {
        const count = $("#basicInfoBody tr").length;
        $("#serverCount").text(count);
        $("#addButtonCount").text(count);
    }

    function getCpuOptions() {
        let options = "";
        cpuModels.forEach(cpu => {
            options += `<option value="${cpu.id}">${cpu.name}</option>`;
        });
        return options;
    }

    function getRamOptions() {
        let options = "";
        ramConfigs.forEach(ram => {
            options += `<option value="${ram.id}">${ram.name}</option>`;
        });
        return options;
    }

    function addNewCpuModel(rowId) {
        const cpuName = prompt("Enter new CPU model name:");
        if (cpuName && cpuName.trim()) {
            $.post("' . $modulelink . '&action=add_cpu_model", {
                name: cpuName.trim(),
                manufacturer: "Generic",
                cores: 0,
                threads: 0
            }, function(response) {
                if (response.success) {
                    // Add to local array
                    cpuModels.push({
                        id: response.id,
                        name: cpuName.trim(),
                        manufacturer: "Generic"
                    });
                    
                    // Update all CPU dropdowns
                    updateAllCpuDropdowns();
                    
                    // Select the new option in the current row
                    $(`#row-${rowId} select[name*="[cpu]"]`).val(response.id);
                    
                    alert("CPU model added successfully!");
                } else {
                    alert("Error adding CPU model: " + response.message);
                }
            }).fail(function() {
                alert("Error adding CPU model");
            });
        }
    }

    function addNewRamConfig(rowId) {
        const ramName = prompt("Enter new RAM configuration (e.g., 32GB DDR4):");
        if (ramName && ramName.trim()) {
            $.post("' . $modulelink . '&action=add_ram_config", {
                name: ramName.trim(),
                type: "DDR4",
                speed: 0,
                capacity_gb: 0
            }, function(response) {
                if (response.success) {
                    // Add to local array
                    ramConfigs.push({
                        id: response.id,
                        name: ramName.trim(),
                        type: "DDR4"
                    });
                    
                    // Update all RAM dropdowns
                    updateAllRamDropdowns();
                    
                    // Select the new option in the current row
                    $(`#row-${rowId} select[name*="[ram]"]`).val(response.id);
                    
                    alert("RAM configuration added successfully!");
                } else {
                    alert("Error adding RAM configuration: " + response.message);
                }
            }).fail(function() {
                alert("Error adding RAM configuration");
            });
        }
    }

    function updateAllCpuDropdowns() {
        const cpuOptions = getCpuOptions();
        $("select[name*='[cpu]']").each(function() {
            const currentValue = $(this).val();
            $(this).html('<option value="">Select CPU Model</option>' + cpuOptions);
            $(this).val(currentValue);
        });
    }

    function updateAllRamDropdowns() {
        const ramOptions = getRamOptions();
        $("select[name*='[ram]']").each(function() {
            const currentValue = $(this).val();
            $(this).html('<option value="">Select RAM Configuration</option>' + ramOptions);
            $(this).val(currentValue);
        });
    }

    function getChassisOptions() {
        const defaultChassis = [
            "Chassis-001",
            "Chassis-002",
            "Chassis-003",
            "Chassis-004",
            "Chassis-005"
        ];
        
        let options = "";
        defaultChassis.forEach(chassis => {
            options += `<option value="${chassis}">${chassis}</option>`;
        });
        return options;
    }

    function getSwitchOptions() {
        const defaultSwitches = [
            "Switch-001",
            "Switch-002",
            "Switch-003",
            "Switch-004",
            "Switch-005"
        ];
        
        let options = "";
        defaultSwitches.forEach(switchName => {
            options += `<option value="${switchName}">${switchName}</option>`;
        });
        return options;
    }

    function submitBulkAdd() {
        const formData = $("#bulkAddForm").serialize();
        
        $.ajax({
            url: "' . $modulelink . '&action=process_bulk_add",
            type: "POST",
            data: formData,
            success: function(response) {
                if (response.success) {
                    alert("Blade servers added successfully!");
                    $("#bulkAddModal").modal("hide");
                    location.reload();
                } else {
                    alert("Error: " + response.message);
                }
            },
            error: function() {
                alert("Error processing bulk add request");
            }
        });
    }

    // Modal cleanup
    $("#bulkAddModal").on("hidden.bs.modal", function() {
        $(this).remove();
    });
    </script>';
}

/**
 * Process bulk add form submission
 */
function dcim_process_bulk_add($modulelink)
{
    header('Content-Type: application/json');
    
    try {
        $basicInfo = $_POST['basic'] ?? [];
        $networkInfo = $_POST['network'] ?? [];
        $chassisInfo = $_POST['chassis'] ?? [];
        $ipmiInfo = $_POST['ipmi'] ?? [];
        
        if (empty($basicInfo)) {
            return json_encode(['success' => false, 'message' => 'No server data provided']);
        }
        
        $successCount = 0;
        $errors = [];
        
        foreach ($basicInfo as $index => $basic) {
            try {
                // Validate required fields
                if (empty($basic['label']) || empty($basic['cpu'])) {
                    $errors[] = "Row $index: Label and CPU are required";
                    continue;
                }
                
                // Get or create chassis
                $chassisId = getOrCreateChassis($chassisInfo[$index]['chassis'] ?? 'Default Chassis');
                
                // Insert blade server
                $serverId = Capsule::table('dcim_blade_servers')->insertGetId([
                    'label' => $basic['label'],
                    'cpu_model_id' => !empty($basic['cpu']) ? (int)$basic['cpu'] : null,
                    'ram_config_id' => !empty($basic['ram']) ? (int)$basic['ram'] : null,
                    'chassis_id' => $chassisId,
                    'status' => strtolower(str_replace(' ', '_', $basic['status'] ?? 'available')),
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s'),
                ]);
                
                // Insert network configuration
                if (!empty($networkInfo[$index]['switch'])) {
                    $switchId = getOrCreateSwitch($networkInfo[$index]['switch']);
                    
                    Capsule::table('dcim_blade_network')->insert([
                        'blade_server_id' => $serverId,
                        'switch_id' => $switchId,
                        'ports' => $networkInfo[$index]['ports'] ?? null,
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s'),
                    ]);
                }
                
                // Insert IPMI configuration
                if (!empty($ipmiInfo[$index]['address'])) {
                    Capsule::table('dcim_blade_ipmi')->insert([
                        'blade_server_id' => $serverId,
                        'ipmi_address' => $ipmiInfo[$index]['address'],
                        'ipmi_password' => $ipmiInfo[$index]['password'] ?? null,
                        'mac_address' => $ipmiInfo[$index]['mac'] ?? null,
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s'),
                    ]);
                }
                
                $successCount++;
                
            } catch (\Exception $e) {
                $errors[] = "Row $index: " . $e->getMessage();
            }
        }
        
        if ($successCount > 0) {
            return json_encode([
                'success' => true, 
                'message' => "$successCount blade servers added successfully",
                'errors' => $errors
            ]);
        } else {
            return json_encode([
                'success' => false, 
                'message' => 'No servers were added',
                'errors' => $errors
            ]);
        }
        
    } catch (\Exception $e) {
        return json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

/**
 * Hardware models management page
 */
function dcim_manage_hardware_models($modulelink)
{
    // Get CPU models
    $cpuModels = Capsule::table('dcim_cpu_models')
        ->orderBy('name')
        ->get();
        
    // Get RAM configurations
    $ramConfigs = Capsule::table('dcim_ram_configs')
        ->orderBy('name')
        ->get();

    return '
    <div class="dcim-modern-container">
        <div class="dcim-header">
            <h1 class="dcim-title">
                <i class="fas fa-microchip"></i>
                Hardware Models
            </h1>
            <p class="dcim-subtitle">Manage CPU models and RAM configurations</p>
        </div>

        <div class="dcim-hardware-tabs">
            <div class="dcim-tabs">
                <button class="dcim-tab dcim-tab-active" data-tab="cpu-models">
                    <i class="fas fa-microchip"></i> CPU Models
                </button>
                <button class="dcim-tab" data-tab="ram-configs">
                    <i class="fas fa-memory"></i> RAM Configurations
                </button>
            </div>
        </div>

        <!-- CPU Models Tab -->
        <div id="cpu-models" class="dcim-tab-content dcim-tab-active">
            <div class="dcim-section-header">
                <h2>CPU Models</h2>
                <button class="dcim-btn dcim-btn-primary" onclick="showAddCpuModal()">
                    <i class="fas fa-plus"></i> Add CPU Model
                </button>
            </div>

            <div class="dcim-hardware-table">
                <table class="dcim-table">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Manufacturer</th>
                            <th>Cores</th>
                            <th>Threads</th>
                            <th>Status</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        ' . generateCpuModelsTable($cpuModels) . '
                    </tbody>
                </table>
            </div>
        </div>

        <!-- RAM Configurations Tab -->
        <div id="ram-configs" class="dcim-tab-content">
            <div class="dcim-section-header">
                <h2>RAM Configurations</h2>
                <button class="dcim-btn dcim-btn-primary" onclick="showAddRamModal()">
                    <i class="fas fa-plus"></i> Add RAM Configuration
                </button>
            </div>

            <div class="dcim-hardware-table">
                <table class="dcim-table">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Type</th>
                            <th>Capacity</th>
                            <th>Speed</th>
                            <th>Status</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        ' . generateRamConfigsTable($ramConfigs) . '
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Add CPU Model Modal -->
    <div class="modal fade" id="addCpuModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add CPU Model</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <form id="addCpuForm">
                    <div class="modal-body">
                        <div class="form-group">
                            <label>Name *</label>
                            <input type="text" class="form-control" name="name" required placeholder="e.g., Intel Xeon E5-2630v3">
                        </div>
                        <div class="form-group">
                            <label>Manufacturer</label>
                            <input type="text" class="form-control" name="manufacturer" placeholder="e.g., Intel">
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Cores</label>
                                    <input type="number" class="form-control" name="cores" min="1" placeholder="8">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Threads</label>
                                    <input type="number" class="form-control" name="threads" min="1" placeholder="16">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Base Frequency</label>
                                    <input type="text" class="form-control" name="base_frequency" placeholder="2.4 GHz">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Max Frequency</label>
                                    <input type="text" class="form-control" name="max_frequency" placeholder="3.1 GHz">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Add CPU Model</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Add RAM Configuration Modal -->
    <div class="modal fade" id="addRamModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add RAM Configuration</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <form id="addRamForm">
                    <div class="modal-body">
                        <div class="form-group">
                            <label>Name *</label>
                            <input type="text" class="form-control" name="name" required placeholder="e.g., 32GB DDR4">
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Type</label>
                                    <select class="form-control" name="type">
                                        <option value="DDR3">DDR3</option>
                                        <option value="DDR4" selected>DDR4</option>
                                        <option value="DDR5">DDR5</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Capacity (GB)</label>
                                    <input type="number" class="form-control" name="capacity_gb" min="1" placeholder="32">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Speed (MHz)</label>
                                    <input type="number" class="form-control" name="speed_mhz" min="1" placeholder="2400">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Form Factor</label>
                                    <select class="form-control" name="form_factor">
                                        <option value="">Select...</option>
                                        <option value="DIMM">DIMM</option>
                                        <option value="SO-DIMM">SO-DIMM</option>
                                        <option value="FB-DIMM">FB-DIMM</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Add RAM Configuration</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
    // Tab switching
    $(".dcim-tab").click(function() {
        const tabId = $(this).data("tab");
        
        $(".dcim-tab").removeClass("dcim-tab-active");
        $(this).addClass("dcim-tab-active");
        
        $(".dcim-tab-content").removeClass("dcim-tab-active");
        $("#" + tabId).addClass("dcim-tab-active");
    });

    function showAddCpuModal() {
        $("#addCpuModal").modal("show");
    }

    function showAddRamModal() {
        $("#addRamModal").modal("show");
    }

    // CPU form submission
    $("#addCpuForm").submit(function(e) {
        e.preventDefault();
        
        $.ajax({
            url: "' . $modulelink . '&action=add_cpu_model",
            type: "POST",
            data: $(this).serialize(),
            success: function(response) {
                if (response.success) {
                    $("#addCpuModal").modal("hide");
                    location.reload();
                } else {
                    alert("Error: " + response.message);
                }
            },
            error: function() {
                alert("Error adding CPU model");
            }
        });
    });

    // RAM form submission
    $("#addRamForm").submit(function(e) {
        e.preventDefault();
        
        $.ajax({
            url: "' . $modulelink . '&action=add_ram_config",
            type: "POST",
            data: $(this).serialize(),
            success: function(response) {
                if (response.success) {
                    $("#addRamModal").modal("hide");
                    location.reload();
                } else {
                    alert("Error: " + response.message);
                }
            },
            error: function() {
                alert("Error adding RAM configuration");
            }
        });
    });

    function deleteCpuModel(id) {
        if (confirm("Are you sure you want to delete this CPU model?")) {
            $.post("' . $modulelink . '&action=delete_cpu_model", {id: id}, function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert("Error: " + response.message);
                }
            });
        }
    }

    function deleteRamConfig(id) {
        if (confirm("Are you sure you want to delete this RAM configuration?")) {
            $.post("' . $modulelink . '&action=delete_ram_config", {id: id}, function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert("Error: " + response.message);
                }
            });
        }
    }
    </script>';
}

/**
 * Generate CPU models table
 */
function generateCpuModelsTable($cpuModels)
{
    $html = '';
    
    if ($cpuModels->isEmpty()) {
        return '<tr><td colspan="7" class="text-center">No CPU models found.</td></tr>';
    }

    foreach ($cpuModels as $cpu) {
        $html .= '
        <tr>
            <td><strong>' . htmlspecialchars($cpu->name) . '</strong></td>
            <td>' . htmlspecialchars($cpu->manufacturer ?: 'N/A') . '</td>
            <td>' . ($cpu->cores ?: 'N/A') . '</td>
            <td>' . ($cpu->threads ?: 'N/A') . '</td>
            <td>
                <span class="dcim-status dcim-status-' . ($cpu->status === 'active' ? 'success' : 'secondary') . '">
                    <i class="fas fa-circle"></i>
                    ' . ucfirst($cpu->status) . '
                </span>
            </td>
            <td>' . date('M j, Y', strtotime($cpu->created_at)) . '</td>
            <td>
                <button class="dcim-btn-icon dcim-btn-danger" onclick="deleteCpuModel(' . $cpu->id . ')" title="Delete">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>';
    }
    
    return $html;
}

/**
 * Generate RAM configurations table
 */
function generateRamConfigsTable($ramConfigs)
{
    $html = '';
    
    if ($ramConfigs->isEmpty()) {
        return '<tr><td colspan="7" class="text-center">No RAM configurations found.</td></tr>';
    }

    foreach ($ramConfigs as $ram) {
        $html .= '
        <tr>
            <td><strong>' . htmlspecialchars($ram->name) . '</strong></td>
            <td>' . htmlspecialchars($ram->type) . '</td>
            <td>' . ($ram->capacity_gb ? $ram->capacity_gb . ' GB' : 'N/A') . '</td>
            <td>' . ($ram->speed_mhz ? $ram->speed_mhz . ' MHz' : 'N/A') . '</td>
            <td>
                <span class="dcim-status dcim-status-' . ($ram->status === 'active' ? 'success' : 'secondary') . '">
                    <i class="fas fa-circle"></i>
                    ' . ucfirst($ram->status) . '
                </span>
            </td>
            <td>' . date('M j, Y', strtotime($ram->created_at)) . '</td>
            <td>
                <button class="dcim-btn-icon dcim-btn-danger" onclick="deleteRamConfig(' . $ram->id . ')" title="Delete">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>';
    }
    
    return $html;
}

/**
 * AJAX endpoints for form data
 */
function getOrCreateChassis($chassisName)
{
    $chassis = Capsule::table('dcim_chassis')->where('name', $chassisName)->first();
    
    if (!$chassis) {
        return Capsule::table('dcim_chassis')->insertGetId([
            'name' => $chassisName,
            'model' => 'Generic Chassis',
            'blade_slots' => 16,
            'status' => 'active',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
        ]);
    }
    
    return $chassis->id;
}

function getOrCreateSwitch($switchName)
{
    $switch = Capsule::table('dcim_switches')->where('name', $switchName)->first();
    
    if (!$switch) {
        return Capsule::table('dcim_switches')->insertGetId([
            'name' => $switchName,
            'model' => 'Generic Switch',
            'port_count' => 48,
            'status' => 'active',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
        ]);
    }
    
    return $switch->id;
}

function dcim_add_cpu_model()
{
    header('Content-Type: application/json');
    
    try {
        $name = trim($_POST['name'] ?? '');
        $manufacturer = trim($_POST['manufacturer'] ?? '');
        $cores = (int)($_POST['cores'] ?? 0);
        $threads = (int)($_POST['threads'] ?? 0);
        
        if (empty($name)) {
            return json_encode(['success' => false, 'message' => 'CPU model name is required']);
        }
        
        // Check if CPU model already exists
        $existing = Capsule::table('dcim_cpu_models')
            ->where('name', $name)
            ->first();
            
        if ($existing) {
            return json_encode(['success' => false, 'message' => 'CPU model already exists']);
        }
        
        $id = Capsule::table('dcim_cpu_models')->insertGetId([
            'name' => $name,
            'manufacturer' => $manufacturer,
            'cores' => $cores,
            'threads' => $threads,
            'status' => 'active',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
        ]);
        
        return json_encode(['success' => true, 'id' => $id, 'message' => 'CPU model added successfully']);
        
    } catch (\Exception $e) {
        return json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

function dcim_add_ram_config()
{
    header('Content-Type: application/json');
    
    try {
        $name = trim($_POST['name'] ?? '');
        $type = trim($_POST['type'] ?? 'DDR4');
        $capacity_gb = (int)($_POST['capacity_gb'] ?? 0);
        $speed_mhz = (int)($_POST['speed_mhz'] ?? 0);
        $form_factor = trim($_POST['form_factor'] ?? '');
        
        if (empty($name)) {
            return json_encode(['success' => false, 'message' => 'RAM configuration name is required']);
        }
        
        // Check if RAM config already exists
        $existing = Capsule::table('dcim_ram_configs')
            ->where('name', $name)
            ->first();
            
        if ($existing) {
            return json_encode(['success' => false, 'message' => 'RAM configuration already exists']);
        }
        
        $id = Capsule::table('dcim_ram_configs')->insertGetId([
            'name' => $name,
            'type' => $type,
            'capacity_gb' => $capacity_gb,
            'speed_mhz' => $speed_mhz,
            'form_factor' => $form_factor,
            'status' => 'active',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
        ]);
        
        return json_encode(['success' => true, 'id' => $id, 'message' => 'RAM configuration added successfully']);
        
    } catch (\Exception $e) {
        return json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

function dcim_delete_cpu_model()
{
    header('Content-Type: application/json');
    
    try {
        $id = (int)($_POST['id'] ?? 0);
        
        if (!$id) {
            return json_encode(['success' => false, 'message' => 'Invalid CPU model ID']);
        }
        
        // Check if CPU model is in use
        $inUse = Capsule::table('dcim_blade_servers')
            ->where('cpu_model_id', $id)
            ->exists();
            
        if ($inUse) {
            return json_encode(['success' => false, 'message' => 'Cannot delete CPU model that is in use by blade servers']);
        }
        
        $deleted = Capsule::table('dcim_cpu_models')
            ->where('id', $id)
            ->delete();
        
        if ($deleted) {
            return json_encode(['success' => true, 'message' => 'CPU model deleted successfully']);
        } else {
            return json_encode(['success' => false, 'message' => 'CPU model not found']);
        }
        
    } catch (\Exception $e) {
        return json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

function dcim_delete_ram_config()
{
    header('Content-Type: application/json');
    
    try {
        $id = (int)($_POST['id'] ?? 0);
        
        if (!$id) {
            return json_encode(['success' => false, 'message' => 'Invalid RAM configuration ID']);
        }
        
        // Check if RAM config is in use
        $inUse = Capsule::table('dcim_blade_servers')
            ->where('ram_config_id', $id)
            ->exists();
            
        if ($inUse) {
            return json_encode(['success' => false, 'message' => 'Cannot delete RAM configuration that is in use by blade servers']);
        }
        
        $deleted = Capsule::table('dcim_ram_configs')
            ->where('id', $id)
            ->delete();
        
        if ($deleted) {
            return json_encode(['success' => true, 'message' => 'RAM configuration deleted successfully']);
        } else {
            return json_encode(['success' => false, 'message' => 'RAM configuration not found']);
        }
        
    } catch (\Exception $e) {
        return json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

function dcim_delete_blade_server()
{
    header('Content-Type: application/json');
    
    try {
        $id = (int)($_POST['id'] ?? 0);
        
        if (!$id) {
            return json_encode(['success' => false, 'message' => 'Invalid server ID']);
        }
        
        // Check if server exists
        $server = Capsule::table('dcim_blade_servers')->where('id', $id)->first();
        if (!$server) {
            return json_encode(['success' => false, 'message' => 'Server not found']);
        }
        
        // Delete related records first (cascade should handle this, but being explicit)
        Capsule::table('dcim_blade_network')->where('blade_server_id', $id)->delete();
        Capsule::table('dcim_blade_ipmi')->where('blade_server_id', $id)->delete();
        
        // Delete the server
        Capsule::table('dcim_blade_servers')->where('id', $id)->delete();
        
        return json_encode(['success' => true, 'message' => 'Blade server deleted successfully']);
        
    } catch (\Exception $e) {
        return json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

/**
 * Blade servers management page
 */
function dcim_blade_servers_page($modulelink)
{
    // Get blade servers with related data
    $bladeServers = Capsule::table('dcim_blade_servers as bs')
        ->leftJoin('dcim_chassis as c', 'bs.chassis_id', '=', 'c.id')
        ->leftJoin('dcim_cpu_models as cpu', 'bs.cpu_model_id', '=', 'cpu.id')
        ->leftJoin('dcim_ram_configs as ram', 'bs.ram_config_id', '=', 'ram.id')
        ->leftJoin('dcim_blade_network as bn', 'bs.id', '=', 'bn.blade_server_id')
        ->leftJoin('dcim_switches as s', 'bn.switch_id', '=', 's.id')
        ->leftJoin('dcim_blade_ipmi as bi', 'bs.id', '=', 'bi.blade_server_id')
        ->select([
            'bs.*',
            'c.name as chassis_name',
            'c.model as chassis_model',
            'cpu.name as cpu_name',
            'ram.name as ram_name',
            's.name as switch_name',
            'bi.ipmi_address',
            'bi.mac_address'
        ])
        ->orderBy('bs.created_at', 'desc')
        ->get();

    // Get statistics
    $stats = [
        'total' => Capsule::table('dcim_blade_servers')->count(),
        'available' => Capsule::table('dcim_blade_servers')->where('status', 'available')->count(),
        'in_use' => Capsule::table('dcim_blade_servers')->where('status', 'in_use')->count(),
        'maintenance' => Capsule::table('dcim_blade_servers')->where('status', 'maintenance')->count(),
        'defect' => Capsule::table('dcim_blade_servers')->where('status', 'defect')->count(),
    ];

    return '
    <div class="dcim-modern-container">
        <div class="dcim-header">
            <h1 class="dcim-title">
                <i class="fas fa-microchip"></i>
                Blade Servers
            </h1>
            <div class="dcim-header-actions">
                <span class="dcim-last-updated">Last updated: ' . date('H:i') . '</span>
                <button class="dcim-btn dcim-btn-primary" onclick="openBulkAddModal()">
                    <i class="fas fa-plus"></i> Add Blade Servers
                </button>
            </div>
        </div>

        <div class="dcim-stats-grid">
            <div class="dcim-stat-card">
                <div class="dcim-stat-icon dcim-stat-primary">
                    <i class="fas fa-server"></i>
                </div>
                <div class="dcim-stat-content">
                    <div class="dcim-stat-number">' . $stats['total'] . '</div>
                    <div class="dcim-stat-label">Total Blade Servers</div>
                </div>
            </div>
            
            <div class="dcim-stat-card">
                <div class="dcim-stat-icon dcim-stat-success">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="dcim-stat-content">
                    <div class="dcim-stat-number">' . $stats['available'] . '</div>
                    <div class="dcim-stat-label">Available</div>
                </div>
            </div>
            
            <div class="dcim-stat-card">
                <div class="dcim-stat-icon dcim-stat-warning">
                    <i class="fas fa-cog"></i>
                </div>
                <div class="dcim-stat-content">
                    <div class="dcim-stat-number">' . $stats['in_use'] . '</div>
                    <div class="dcim-stat-label">In Use</div>
                </div>
            </div>
            
            <div class="dcim-stat-card">
                <div class="dcim-stat-icon dcim-stat-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="dcim-stat-content">
                    <div class="dcim-stat-number">' . $stats['defect'] . '</div>
                    <div class="dcim-stat-label">Defect</div>
                </div>
            </div>
        </div>

        <div class="dcim-filters-section">
            <div class="dcim-filter-group">
                <select class="dcim-filter" id="statusFilter">
                    <option value="">All Status</option>
                    <option value="available">Available</option>
                    <option value="in_use">In Use</option>
                    <option value="maintenance">Maintenance</option>
                    <option value="defect">Defect</option>
                </select>
                
                <select class="dcim-filter" id="chassisFilter">
                    <option value="">All Chassis</option>
                    ' . getChassisFilterOptions() . '
                </select>
                
                <select class="dcim-filter" id="cpuFilter">
                    <option value="">All CPUs</option>
                    ' . getCpuFilterOptions() . '
                </select>
            </div>
            
            <div class="dcim-search-box">
                <i class="fas fa-search"></i>
                <input type="text" placeholder="Search blade servers..." id="blade-search">
            </div>
        </div>

        <div class="dcim-blade-servers-table">
            <table class="dcim-table" id="bladeServersTable">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Label</th>
                        <th>CPU</th>
                        <th>RAM</th>
                        <th>Chassis</th>
                        <th>Switch</th>
                        <th>IPMI</th>
                        <th>MAC Address</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    ' . generateBladeServersTable($bladeServers) . '
                </tbody>
            </table>
        </div>
    </div>

    <script>
    function openBulkAddModal() {
        loadBulkAddModal();
    }

    function loadBulkAddModal() {
        $.get("' . $modulelink . '&action=bulk_add_blade_servers", function(data) {
            $("body").append(data);
            $("#bulkAddModal").modal("show");
        });
    }

    function editBladeServer(id) {
        // Implement edit functionality
        alert("Edit blade server ID: " + id);
    }

    function deleteBladeServer(id) {
        if (confirm("Are you sure you want to delete this blade server?")) {
            $.post("' . $modulelink . '&action=delete_blade_server", {id: id}, function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert("Error: " + response.message);
                }
            });
        }
    }

    function showBladeServerDetails(id) {
        // Implement details modal
        alert("Show details for blade server ID: " + id);
    }

    // Filter functionality
    $("#statusFilter, #chassisFilter, #cpuFilter").on("change", function() {
        filterBladeServers();
    });

    $("#blade-search").on("keyup", function() {
        filterBladeServers();
    });

    function filterBladeServers() {
        const statusFilter = $("#statusFilter").val();
        const chassisFilter = $("#chassisFilter").val();
        const cpuFilter = $("#cpuFilter").val();
        const searchTerm = $("#blade-search").val().toLowerCase();

        $("#bladeServersTable tbody tr").each(function() {
            const row = $(this);
            const status = row.find(".dcim-status").text().toLowerCase();
            const chassis = row.find("td:nth-child(5)").text().toLowerCase();
            const cpu = row.find("td:nth-child(3)").text().toLowerCase();
            const label = row.find("td:nth-child(2)").text().toLowerCase();

            let show = true;

            if (statusFilter && !status.includes(statusFilter)) show = false;
            if (chassisFilter && !chassis.includes(chassisFilter.toLowerCase())) show = false;
            if (cpuFilter && !cpu.includes(cpuFilter.toLowerCase())) show = false;
            if (searchTerm && !label.includes(searchTerm) && !cpu.includes(searchTerm)) show = false;

            row.toggle(show);
        });
    }
    </script>';
}

/**
 * Generate blade servers table
 */
function generateBladeServersTable($bladeServers)
{
    $html = '';
    
    if ($bladeServers->isEmpty()) {
        return '<tr><td colspan="10" class="text-center">No blade servers found. <a href="#" onclick="openBulkAddModal()">Add some blade servers</a></td></tr>';
    }

    foreach ($bladeServers as $server) {
        $statusClass = getBladeStatusClass($server->status);
        $statusText = getBladeStatusText($server->status);
        
        $html .= '
        <tr class="dcim-table-row" onclick="showBladeServerDetails(' . $server->id . ')">
            <td>#' . $server->id . '</td>
            <td><strong>' . htmlspecialchars($server->label) . '</strong></td>
            <td>' . htmlspecialchars($server->cpu_name ?: 'Not specified') . '</td>
            <td>' . htmlspecialchars($server->ram_name ?: 'Not specified') . '</td>
            <td>' . htmlspecialchars($server->chassis_name ?: 'Not assigned') . '</td>
            <td>' . htmlspecialchars($server->switch_name ?: 'Not connected') . '</td>
            <td>' . htmlspecialchars($server->ipmi_address ?: 'Not configured') . '</td>
            <td>' . htmlspecialchars($server->mac_address ?: 'Not detected') . '</td>
            <td>
                <span class="dcim-status dcim-status-' . $statusClass . '">
                    <i class="fas fa-circle"></i>
                    ' . $statusText . '
                </span>
            </td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="dcim-btn-icon" onclick="event.stopPropagation(); editBladeServer(' . $server->id . ')" title="Edit">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="dcim-btn-icon dcim-btn-danger" onclick="event.stopPropagation(); deleteBladeServer(' . $server->id . ')" title="Delete">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>';
    }
    
    return $html;
}

/**
 * Get blade server status class
 */
function getBladeStatusClass($status)
{
    switch ($status) {
        case 'available': return 'success';
        case 'in_use': return 'warning';
        case 'maintenance': return 'info';
        case 'defect': return 'danger';
        default: return 'secondary';
    }
}

/**
 * Get blade server status text
 */
function getBladeStatusText($status)
{
    switch ($status) {
        case 'available': return 'Available';
        case 'in_use': return 'In Use';
        case 'maintenance': return 'Maintenance';
        case 'defect': return 'Defect';
        default: return 'Unknown';
    }
}

/**
 * Get chassis filter options
 */
function getChassisFilterOptions()
{
    try {
        $chassis = Capsule::table('dcim_chassis')
            ->select('name')
            ->distinct()
            ->orderBy('name')
            ->get();
        
        $options = '';
        foreach ($chassis as $item) {
            $options .= '<option value="' . htmlspecialchars($item->name) . '">' . htmlspecialchars($item->name) . '</option>';
        }
        
        return $options;
    } catch (\Exception $e) {
        return '';
    }
}

/**
 * Get CPU filter options
 */
function getCpuFilterOptions()
{
    try {
        $cpus = Capsule::table('dcim_cpu_models')
            ->select('name')
            ->distinct()
            ->where('status', 'active')
            ->orderBy('name')
            ->get();
        
        $options = '';
        foreach ($cpus as $cpu) {
            $options .= '<option value="' . htmlspecialchars($cpu->name) . '">' . htmlspecialchars($cpu->name) . '</option>';
        }
        
        return $options;
    } catch (\Exception $e) {
        return '';
    }
}
function dcim_get_chassis_list()
{
    header('Content-Type: application/json');
    
    try {
        $chassis = Capsule::table('dcim_chassis')
            ->where('status', 'active')
            ->select(['id', 'name', 'model', 'blade_slots'])
            ->get();
            
        return json_encode($chassis);
    } catch (\Exception $e) {
        return json_encode([]);
    }
}

function dcim_get_switch_list()
{
    header('Content-Type: application/json');
    
    try {
        $switches = Capsule::table('dcim_switches')
            ->where('status', 'active')
            ->select(['id', 'name', 'model', 'port_count'])
            ->get();
            
        return json_encode($switches);
    } catch (\Exception $e) {
        return json_encode([]);
    }
}

function dcim_get_cpu_models()
{
    header('Content-Type: application/json');
    
    try {
        $cpuModels = Capsule::table('dcim_cpu_models')
            ->where('status', 'active')
            ->select(['id', 'name', 'manufacturer', 'cores', 'threads'])
            ->orderBy('name')
            ->get();
            
        return json_encode($cpuModels);
    } catch (\Exception $e) {
        return json_encode([]);
    }
}

function dcim_get_ram_configs()
{
    header('Content-Type: application/json');
    
    try {
        $ramConfigs = Capsule::table('dcim_ram_configs')
            ->where('status', 'active')
            ->select(['id', 'name', 'type', 'capacity_gb', 'speed_mhz'])
            ->orderBy('name')
            ->get();
            
        return json_encode($ramConfigs);
    } catch (\Exception $e) {
        return json_encode([]);
    }
}

/**
 * Helper functions (keeping existing ones)
 */
function generateInventoryTable($servers)
{
    $html = '';
    foreach ($servers as $server) {
        $statusClass = getStatusClass($server->status);
        $statusText = getStatusText($server->status);
        
        $html .= '
        <tr class="dcim-table-row" onclick="showServerDetails(\'' . $server->id . '\')">
            <td>#' . $server->id . '</td>
            <td><strong>' . $server->hostname . '</strong></td>
            <td>' . ($server->cpu_specs ?: 'Dual Intel Xeon E5-2630v3') . '</td>
            <td>
                ' . $server->location_city . ' (' . $server->location_name . '), ' . $server->location_country . '
            </td>
            <td>' . $server->rack_name . ' (' . ($server->u_size ?: 1) . ')</td>
            <td>1.2TB</td>
            <td>1</td>
            <td>Unassigned</td>
            <td>
                <span class="dcim-status dcim-status-' . $statusClass . '">
                    <i class="fas fa-circle"></i>
                    ' . $statusText . '
                </span>
            </td>
            <td>
                <button class="dcim-btn-icon dcim-btn-danger" title="Delete">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>';
    }
    return $html;
}

function getStatusClass($status)
{
    switch ($status) {
        case 'active': return 'success';
        case 'provisioning': return 'warning';
        case 'failed': return 'danger';
        case 'maintenance': return 'info';
        default: return 'secondary';
    }
}

function getStatusText($status)
{
    switch ($status) {
        case 'active': return 'Available';
        case 'provisioning': return 'Installing';
        case 'failed': return 'Failed';
        case 'maintenance': return 'Maintenance';
        default: return 'Unknown';
    }
}

/**
 * AJAX endpoints
 */
function dcim_ajax_stats()
{
    header('Content-Type: application/json');
    
    try {
        $stats = [
            'locations' => Capsule::table('dcim_locations')->count(),
            'total_servers' => Capsule::table('dcim_servers')->count(),
            'available_servers' => Capsule::table('dcim_servers')->where('status', 'active')->count(),
            'defect_servers' => Capsule::table('dcim_servers')->where('status', 'failed')->count(),
        ];
        
        return json_encode(['success' => true, 'stats' => $stats]);
    } catch (\Exception $e) {
        return json_encode(['success' => false, 'error' => $e->getMessage()]);
    }
}

/**
 * Database setup and helper functions
 */
function createDcimTables()
{
    try {
        // Create locations table
        if (!Capsule::schema()->hasTable('dcim_locations')) {
            Capsule::schema()->create('dcim_locations', function ($table) {
                $table->increments('id');
                $table->string('name');
                $table->enum('type', ['region', 'facility', 'floor', 'room'])->default('facility');
                $table->string('city', 100)->nullable();
                $table->string('country', 100)->nullable();
                $table->enum('status', ['active', 'inactive', 'maintenance'])->default('active');
                $table->timestamps();
            });
        }

        // Create racks table
        if (!Capsule::schema()->hasTable('dcim_racks')) {
            Capsule::schema()->create('dcim_racks', function ($table) {
                $table->increments('id');
                $table->integer('location_id')->unsigned();
                $table->string('name');
                $table->integer('height_u')->default(42);
                $table->enum('status', ['active', 'inactive', 'maintenance'])->default('active');
                $table->timestamps();
                
                $table->foreign('location_id')->references('id')->on('dcim_locations')->onDelete('cascade');
            });
        }

        // Create servers table
        if (!Capsule::schema()->hasTable('dcim_servers')) {
            Capsule::schema()->create('dcim_servers', function ($table) {
                $table->increments('id');
                $table->integer('rack_id')->unsigned();
                $table->string('hostname');
                $table->string('asset_tag', 100)->nullable();
                $table->integer('u_position')->default(1);
                $table->integer('u_size')->default(1);
                $table->string('cpu_specs')->nullable();
                $table->enum('status', ['active', 'provisioning', 'failed', 'maintenance'])->default('active');
                $table->timestamps();
                
                $table->foreign('rack_id')->references('id')->on('dcim_racks')->onDelete('cascade');
            });
        }

        // Create chassis table
        if (!Capsule::schema()->hasTable('dcim_chassis')) {
            Capsule::schema()->create('dcim_chassis', function ($table) {
                $table->increments('id');
                $table->string('name');
                $table->string('model')->nullable();
                $table->integer('blade_slots')->default(16);
                $table->enum('status', ['active', 'inactive', 'maintenance'])->default('active');
                $table->timestamps();
            });
        }

        // Create switches table
        if (!Capsule::schema()->hasTable('dcim_switches')) {
            Capsule::schema()->create('dcim_switches', function ($table) {
                $table->increments('id');
                $table->string('name');
                $table->string('model')->nullable();
                $table->integer('port_count')->default(48);
                $table->enum('status', ['active', 'inactive', 'maintenance'])->default('active');
                $table->timestamps();
            });
        }

        // Create CPU models table
        if (!Capsule::schema()->hasTable('dcim_cpu_models')) {
            Capsule::schema()->create('dcim_cpu_models', function ($table) {
                $table->increments('id');
                $table->string('name');
                $table->string('manufacturer')->nullable();
                $table->integer('cores')->default(0);
                $table->integer('threads')->default(0);
                $table->string('base_frequency')->nullable();
                $table->string('max_frequency')->nullable();
                $table->enum('status', ['active', 'inactive'])->default('active');
                $table->timestamps();
                
                $table->unique('name');
            });
        }

        // Create RAM configurations table
        if (!Capsule::schema()->hasTable('dcim_ram_configs')) {
            Capsule::schema()->create('dcim_ram_configs', function ($table) {
                $table->increments('id');
                $table->string('name');
                $table->string('type', 10)->default('DDR4'); // DDR3, DDR4, DDR5
                $table->integer('capacity_gb')->default(0);
                $table->integer('speed_mhz')->default(0);
                $table->string('form_factor')->nullable(); // DIMM, SO-DIMM, etc.
                $table->enum('status', ['active', 'inactive'])->default('active');
                $table->timestamps();
                
                $table->unique('name');
            });
        }
        if (!Capsule::schema()->hasTable('dcim_blade_servers')) {
            Capsule::schema()->create('dcim_blade_servers', function ($table) {
                $table->increments('id');
                $table->string('label');
                $table->string('cpu_model')->nullable();
                $table->string('ram_config')->nullable();
                $table->integer('chassis_id')->unsigned();
                $table->enum('status', ['available', 'in_use', 'maintenance', 'defect'])->default('available');
                $table->timestamps();
                
                $table->foreign('chassis_id')->references('id')->on('dcim_chassis')->onDelete('cascade');
            });
        }

        // Create blade network table
        if (!Capsule::schema()->hasTable('dcim_blade_network')) {
            Capsule::schema()->create('dcim_blade_network', function ($table) {
                $table->increments('id');
                $table->integer('blade_server_id')->unsigned();
                $table->integer('switch_id')->unsigned();
                $table->string('ports')->nullable();
                $table->timestamps();
                
                $table->foreign('blade_server_id')->references('id')->on('dcim_blade_servers')->onDelete('cascade');
                $table->foreign('switch_id')->references('id')->on('dcim_switches')->onDelete('cascade');
            });
        }

        // Create blade IPMI table
        if (!Capsule::schema()->hasTable('dcim_blade_ipmi')) {
            Capsule::schema()->create('dcim_blade_ipmi', function ($table) {
                $table->increments('id');
                $table->integer('blade_server_id')->unsigned();
                $table->string('ipmi_address')->nullable();
                $table->string('ipmi_password')->nullable();
                $table->string('mac_address')->nullable();
                $table->timestamps();
                
                $table->foreign('blade_server_id')->references('id')->on('dcim_blade_servers')->onDelete('cascade');
            });
        }

        return ['success' => true, 'message' => 'Tables created successfully'];
    } catch (\Exception $e) {
        return ['success' => false, 'message' => $e->getMessage()];
    }
}

function checkDcimTablesExist()
{
    try {
        return Capsule::schema()->hasTable('dcim_locations') && 
               Capsule::schema()->hasTable('dcim_racks') && 
               Capsule::schema()->hasTable('dcim_servers') &&
               Capsule::schema()->hasTable('dcim_chassis') &&
               Capsule::schema()->hasTable('dcim_cpu_models') &&
               Capsule::schema()->hasTable('dcim_ram_configs') &&
               Capsule::schema()->hasTable('dcim_blade_servers');
    } catch (\Exception $e) {
        return false;
    }
}

function createSampleData()
{
    try {
        // Create sample locations
        $locations = [
            ['name' => 'Bucharest DC1', 'city' => 'Bucharest', 'country' => 'Romania'],
            ['name' => 'Frankfurt DC1', 'city' => 'Frankfurt', 'country' => 'Germany'],
            ['name' => 'Amsterdam DC1', 'city' => 'Amsterdam', 'country' => 'Netherlands'],
        ];

        foreach ($locations as $location) {
            $locationId = Capsule::table('dcim_locations')->insertGetId([
                'name' => $location['name'],
                'type' => 'facility',
                'city' => $location['city'],
                'country' => $location['country'],
                'status' => 'active',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ]);

            // Create sample racks
            for ($i = 1; $i <= 5; $i++) {
                $rackId = Capsule::table('dcim_racks')->insertGetId([
                    'location_id' => $locationId,
                    'name' => 'Rack-' . str_pad($i, 3, '0', STR_PAD_LEFT),
                    'height_u' => 42,
                    'status' => 'active',
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s'),
                ]);

                // Create sample servers
                for ($j = 1; $j <= 3; $j++) {
                    Capsule::table('dcim_servers')->insert([
                        'rack_id' => $rackId,
                        'hostname' => 'XR-BL' . $i . 'S' . $j,
                        'asset_tag' => 'SRV-' . str_pad(($i * 10 + $j), 3, '0', STR_PAD_LEFT),
                        'u_position' => $j,
                        'u_size' => 1,
                        'cpu_specs' => 'Dual Intel Xeon E5-2630v3',
                        'status' => $j == 1 ? 'active' : ($j == 2 ? 'provisioning' : 'failed'),
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s'),
                    ]);
                }
            }
        }

        // Create default CPU models
        $cpuModels = [
            ['name' => 'Intel Xeon E5-2630v3', 'manufacturer' => 'Intel', 'cores' => 8, 'threads' => 16, 'base_frequency' => '2.4 GHz', 'max_frequency' => '3.2 GHz'],
            ['name' => 'Intel Xeon E5-2650v4', 'manufacturer' => 'Intel', 'cores' => 12, 'threads' => 24, 'base_frequency' => '2.2 GHz', 'max_frequency' => '2.9 GHz'],
            ['name' => 'Intel Xeon Silver 4214', 'manufacturer' => 'Intel', 'cores' => 12, 'threads' => 24, 'base_frequency' => '2.2 GHz', 'max_frequency' => '3.2 GHz'],
            ['name' => 'Intel Xeon Gold 5218', 'manufacturer' => 'Intel', 'cores' => 16, 'threads' => 32, 'base_frequency' => '2.3 GHz', 'max_frequency' => '3.9 GHz'],
            ['name' => 'AMD EPYC 7402P', 'manufacturer' => 'AMD', 'cores' => 24, 'threads' => 48, 'base_frequency' => '2.8 GHz', 'max_frequency' => '3.35 GHz'],
            ['name' => 'AMD EPYC 7502P', 'manufacturer' => 'AMD', 'cores' => 32, 'threads' => 64, 'base_frequency' => '2.5 GHz', 'max_frequency' => '3.35 GHz'],
        ];

        foreach ($cpuModels as $cpu) {
            if (!Capsule::table('dcim_cpu_models')->where('name', $cpu['name'])->exists()) {
                Capsule::table('dcim_cpu_models')->insert([
                    'name' => $cpu['name'],
                    'manufacturer' => $cpu['manufacturer'],
                    'cores' => $cpu['cores'],
                    'threads' => $cpu['threads'],
                    'base_frequency' => $cpu['base_frequency'],
                    'max_frequency' => $cpu['max_frequency'],
                    'status' => 'active',
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s'),
                ]);
            }
        }

        // Create default RAM configurations
        $ramConfigs = [
            ['name' => '16GB DDR4', 'type' => 'DDR4', 'capacity_gb' => 16, 'speed_mhz' => 2400, 'form_factor' => 'DIMM'],
            ['name' => '32GB DDR4', 'type' => 'DDR4', 'capacity_gb' => 32, 'speed_mhz' => 2400, 'form_factor' => 'DIMM'],
            ['name' => '64GB DDR4', 'type' => 'DDR4', 'capacity_gb' => 64, 'speed_mhz' => 2666, 'form_factor' => 'DIMM'],
            ['name' => '128GB DDR4', 'type' => 'DDR4', 'capacity_gb' => 128, 'speed_mhz' => 2933, 'form_factor' => 'DIMM'],
            ['name' => '256GB DDR4', 'type' => 'DDR4', 'capacity_gb' => 256, 'speed_mhz' => 3200, 'form_factor' => 'DIMM'],
            ['name' => '512GB DDR4', 'type' => 'DDR4', 'capacity_gb' => 512, 'speed_mhz' => 3200, 'form_factor' => 'DIMM'],
        ];

        foreach ($ramConfigs as $ram) {
            if (!Capsule::table('dcim_ram_configs')->where('name', $ram['name'])->exists()) {
                Capsule::table('dcim_ram_configs')->insert([
                    'name' => $ram['name'],
                    'type' => $ram['type'],
                    'capacity_gb' => $ram['capacity_gb'],
                    'speed_mhz' => $ram['speed_mhz'],
                    'form_factor' => $ram['form_factor'],
                    'status' => 'active',
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s'),
                ]);
            }
        }
        for ($i = 1; $i <= 5; $i++) {
            Capsule::table('dcim_chassis')->insert([
                'name' => 'Chassis-' . str_pad($i, 3, '0', STR_PAD_LEFT),
                'model' => 'Generic Blade Chassis',
                'blade_slots' => 16,
                'status' => 'active',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ]);
        }

        // Create sample switches
        for ($i = 1; $i <= 5; $i++) {
            Capsule::table('dcim_switches')->insert([
                'name' => 'Switch-' . str_pad($i, 3, '0', STR_PAD_LEFT),
                'model' => 'Generic Network Switch',
                'port_count' => 48,
                'status' => 'active',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ]);
        }

    } catch (\Exception $e) {
        error_log("DCIM sample data creation error: " . $e->getMessage());
    }
}

function dcim_database_setup_page($modulelink)
{
    return '
    <div class="dcim-setup-page">
        <div class="alert alert-warning">
            <h4><i class="fas fa-exclamation-triangle"></i> Database Setup Required</h4>
            <p>The DCIM database tables need to be created. Please deactivate and reactivate the module to set up the database.</p>
        </div>
    </div>';
}

function dcim_error_page($message)
{
    return '
    <div class="dcim-error-page">
        <div class="alert alert-danger">
            <h4><i class="fas fa-exclamation-triangle"></i> Error</h4>
            <p>' . htmlspecialchars($message) . '</p>
        </div>
    </div>';
}