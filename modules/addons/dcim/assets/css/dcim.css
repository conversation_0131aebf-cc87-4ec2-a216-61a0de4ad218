/**
 * DCIM Module CSS Styles
 * 
 * Custom styles for the DCIM addon module
 */

/* General Styles */
.dcim-dashboard {
    margin-bottom: 20px;
}

.dcim-quick-action {
    margin-bottom: 20px;
}

.dcim-quick-action .btn {
    height: 120px;
    padding: 15px;
    text-align: center;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.dcim-quick-action .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.dcim-quick-action .fa-2x {
    margin-bottom: 10px;
    display: block;
}

/* Filter Forms */
.dcim-filter-form {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 20px;
}

.dcim-filter-form .form-group {
    margin-right: 10px;
    margin-bottom: 10px;
}

.dcim-filter-form .form-control {
    min-width: 150px;
}

/* Tables */
.dcim-table {
    margin-top: 20px;
}

.dcim-table .table {
    background: white;
}

.dcim-table .table th {
    background: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
}

.dcim-table .table th a {
    color: #495057;
    text-decoration: none;
}

.dcim-table .table th a:hover {
    color: #007bff;
}

.dcim-table .table td {
    vertical-align: middle;
}

/* Status Labels */
.status-active {
    background-color: #28a745;
}

.status-inactive {
    background-color: #6c757d;
}

.status-maintenance {
    background-color: #ffc107;
    color: #212529;
}

.status-reserved {
    background-color: #17a2b8;
}

.status-provisioning {
    background-color: #007bff;
}

.status-decommissioned {
    background-color: #dc3545;
}

.status-failed {
    background-color: #dc3545;
}

.status-available {
    background-color: #28a745;
}

.status-assigned {
    background-color: #007bff;
}

.status-blacklisted {
    background-color: #343a40;
}

/* Forms */
.dcim-form {
    margin-bottom: 20px;
}

.dcim-form .panel {
    margin-bottom: 20px;
}

.dcim-form .form-horizontal .control-label {
    font-weight: 600;
}

.dcim-form .help-block {
    font-size: 12px;
    color: #6c757d;
}

/* Rack Visualization */
.dcim-rack-visual {
    border: 2px solid #333;
    background: #f8f9fa;
    margin: 20px 0;
    position: relative;
    min-height: 400px;
}

.dcim-rack-header {
    background: #333;
    color: white;
    padding: 10px;
    text-align: center;
    font-weight: bold;
}

.dcim-rack-units {
    display: flex;
    flex-direction: column-reverse;
    padding: 10px;
}

.dcim-rack-unit {
    height: 20px;
    border: 1px solid #ddd;
    margin-bottom: 1px;
    display: flex;
    align-items: center;
    padding: 0 5px;
    font-size: 11px;
    position: relative;
}

.dcim-rack-unit.occupied {
    background: #007bff;
    color: white;
}

.dcim-rack-unit.reserved {
    background: #ffc107;
    color: #212529;
}

.dcim-rack-unit.maintenance {
    background: #dc3545;
    color: white;
}

.dcim-rack-unit-number {
    position: absolute;
    left: 5px;
    font-weight: bold;
}

.dcim-rack-unit-content {
    margin-left: 30px;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* IP Address Management */
.dcim-ip-subnet {
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-bottom: 15px;
}

.dcim-ip-subnet-header {
    background: #f8f9fa;
    padding: 10px 15px;
    border-bottom: 1px solid #ddd;
    font-weight: bold;
}

.dcim-ip-subnet-content {
    padding: 15px;
}

.dcim-ip-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 5px;
    margin-top: 10px;
}

.dcim-ip-address {
    padding: 5px 8px;
    border: 1px solid #ddd;
    border-radius: 3px;
    text-align: center;
    font-size: 11px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.dcim-ip-address:hover {
    border-color: #007bff;
    background: #f8f9fa;
}

.dcim-ip-address.available {
    background: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.dcim-ip-address.assigned {
    background: #cce5ff;
    border-color: #99d6ff;
    color: #004085;
}

.dcim-ip-address.reserved {
    background: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

.dcim-ip-address.blacklisted {
    background: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

/* Statistics Cards */
.dcim-stat-card {
    background: white;
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 20px;
    text-align: center;
    margin-bottom: 20px;
    transition: all 0.3s ease;
}

.dcim-stat-card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.dcim-stat-card .stat-number {
    font-size: 2.5em;
    font-weight: bold;
    color: #007bff;
    margin-bottom: 5px;
}

.dcim-stat-card .stat-label {
    color: #6c757d;
    font-size: 0.9em;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Progress Bars */
.dcim-progress {
    margin-bottom: 15px;
}

.dcim-progress .progress-label {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
    font-size: 0.9em;
}

.dcim-progress .progress {
    height: 20px;
}

/* Sidebar */
.dcim-sidebar {
    margin-bottom: 20px;
}

.dcim-sidebar .panel {
    margin-bottom: 15px;
}

.dcim-sidebar .list-group-item {
    border-left: 3px solid transparent;
    transition: all 0.2s ease;
}

.dcim-sidebar .list-group-item:hover {
    border-left-color: #007bff;
    background: #f8f9fa;
}

.dcim-sidebar .list-group-item i {
    margin-right: 8px;
    width: 16px;
    text-align: center;
}

/* Responsive Design */
@media (max-width: 768px) {
    .dcim-quick-action .btn {
        height: auto;
        padding: 15px 10px;
    }
    
    .dcim-filter-form .form-group {
        margin-bottom: 10px;
        width: 100%;
    }
    
    .dcim-filter-form .form-control {
        min-width: auto;
        width: 100%;
    }
    
    .dcim-ip-grid {
        grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    }
    
    .dcim-rack-visual {
        min-height: 300px;
    }
    
    .dcim-rack-unit {
        height: 15px;
        font-size: 10px;
    }
}

/* Loading States */
.dcim-loading {
    text-align: center;
    padding: 40px;
    color: #6c757d;
}

.dcim-loading i {
    font-size: 2em;
    margin-bottom: 10px;
}

/* Error States */
.dcim-error {
    text-align: center;
    padding: 40px;
    color: #dc3545;
}

.dcim-error i {
    font-size: 2em;
    margin-bottom: 10px;
}

/* Empty States */
.dcim-empty {
    text-align: center;
    padding: 40px;
    color: #6c757d;
}

.dcim-empty i {
    font-size: 3em;
    margin-bottom: 15px;
    opacity: 0.5;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.dcim-fade-in {
    animation: fadeIn 0.3s ease-out;
}

/* Print Styles */
@media print {
    .dcim-sidebar,
    .dcim-filter-form,
    .btn,
    .pagination {
        display: none !important;
    }
    
    .dcim-table {
        margin-top: 0;
    }
    
    .panel {
        border: 1px solid #ddd !important;
        box-shadow: none !important;
    }
}
