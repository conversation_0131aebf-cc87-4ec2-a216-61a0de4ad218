<?php
/**
 * DCIM Module Hooks - Modern Design
 * 
 * This file contains hooks for the DCIM module to inject modern CSS and JavaScript
 * into the admin area when the module is being used.
 */

if (!defined("WHMCS")) {
    die("This file cannot be accessed directly");
}

/**
 * Hook to add modern CSS to admin area head section
 */
add_hook('AdminAreaHeadOutput', 1, function($vars) {
    $moduleName = $_GET['module'] ?? '';
    
    // Only add CSS when DCIM module is active
    if ($moduleName == 'dcim') {
        return '
        <style>
        /* DCIM Modern Design System */
        .dcim-modern-container {
            background: #f8fafc;
            min-height: 100vh;
            padding: 24px;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        }

        /* Header Styles */
        .dcim-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 32px;
        }

        .dcim-title {
            font-size: 32px;
            font-weight: 700;
            color: #1a202c;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .dcim-title i {
            color: #667eea;
        }

        .dcim-subtitle {
            color: #718096;
            margin: 4px 0 0 0;
            font-size: 16px;
        }

        .dcim-header-actions {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .dcim-last-updated {
            color: #718096;
            font-size: 14px;
        }

        /* Button Styles */
        .dcim-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
        }

        .dcim-btn-primary {
            background: #667eea;
            color: white;
        }

        .dcim-btn-primary:hover {
            background: #5a67d8;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .dcim-btn-icon {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: none;
            background: transparent;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
            color: #718096;
        }

        .dcim-btn-icon:hover {
            background: #edf2f7;
            color: #4a5568;
        }

        .dcim-btn-danger {
            color: #e53e3e;
        }

        .dcim-btn-danger:hover {
            background: #fed7d7;
            color: #c53030;
        }

        /* Stats Grid */
        .dcim-stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
        }

        .dcim-stat-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            display: flex;
            align-items: center;
            gap: 16px;
            border: 1px solid #e2e8f0;
            transition: all 0.2s ease;
        }

        .dcim-stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
        }

        .dcim-stat-icon {
            width: 56px;
            height: 56px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }

        .dcim-stat-primary {
            background: #e6f3ff;
            color: #0066cc;
        }

        .dcim-stat-success {
            background: #e6fffa;
            color: #00a86b;
        }

        .dcim-stat-warning {
            background: #fffbeb;
            color: #d69e2e;
        }

        .dcim-stat-info {
            background: #ebf8ff;
            color: #3182ce;
        }

        .dcim-stat-danger {
            background: #fed7d7;
            color: #e53e3e;
        }

        .dcim-stat-number {
            font-size: 36px;
            font-weight: 700;
            color: #1a202c;
            line-height: 1;
        }

        .dcim-stat-label {
            font-size: 14px;
            color: #718096;
            margin-top: 4px;
        }

        /* Action Cards */
        .dcim-quick-actions {
            margin-bottom: 32px;
        }

        .dcim-action-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 24px;
        }

        .dcim-action-card {
            background: white;
            border-radius: 12px;
            padding: 32px 24px;
            text-align: center;
            border: 1px solid #e2e8f0;
            transition: all 0.2s ease;
            text-decoration: none;
            color: inherit;
        }

        .dcim-action-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 28px rgba(0, 0, 0, 0.12);
            text-decoration: none;
            color: inherit;
        }

        .dcim-action-icon {
            width: 64px;
            height: 64px;
            background: #f7fafc;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 16px;
            font-size: 28px;
            color: #667eea;
        }

        .dcim-action-card h3 {
            font-size: 18px;
            font-weight: 600;
            color: #1a202c;
            margin: 0 0 8px 0;
        }

        .dcim-action-card p {
            color: #718096;
            margin: 0;
            font-size: 14px;
            line-height: 1.5;
        }

        /* Locations Layout */
        .dcim-locations-layout {
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 32px;
        }

        .dcim-locations-sidebar {
            background: white;
            border-radius: 12px;
            padding: 24px;
            border: 1px solid #e2e8f0;
            height: fit-content;
        }

        .dcim-locations-content {
            background: white;
            border-radius: 12px;
            padding: 24px;
            border: 1px solid #e2e8f0;
        }

        .dcim-content-header {
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e2e8f0;
        }

        .dcim-content-header h2 {
            font-size: 20px;
            font-weight: 600;
            color: #1a202c;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        /* Search Box */
        .dcim-search-box {
            position: relative;
            margin-bottom: 24px;
        }

        .dcim-search-box i {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #718096;
        }

        .dcim-search-box input {
            width: 100%;
            padding: 10px 12px 10px 36px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
            background: #f8fafc;
            transition: all 0.2s ease;
        }

        .dcim-search-box input:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        /* Countries List */
        .dcim-countries-list {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .dcim-country-item {
            border-radius: 8px;
            transition: all 0.2s ease;
        }

        .dcim-country-item:hover {
            background: #f8fafc;
        }

        .dcim-country-selected {
            background: #e6f3ff;
            border-left: 3px solid #667eea;
        }

        .dcim-country-item a {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 16px;
            text-decoration: none;
            color: #4a5568;
            font-weight: 500;
        }

        .dcim-country-item a:hover {
            text-decoration: none;
            color: #1a202c;
        }

        .dcim-country-actions {
            display: flex;
            gap: 4px;
            padding: 0 16px 12px;
        }

        /* Racks Grid */
        .dcim-racks-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
        }

        .dcim-rack-card {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            transition: all 0.2s ease;
            position: relative;
        }

        .dcim-rack-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
        }

        .dcim-rack-number {
            font-size: 36px;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 8px;
        }

        .dcim-rack-capacity {
            font-size: 14px;
            color: #718096;
            margin-bottom: 12px;
        }

        .dcim-rack-location {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .dcim-rack-city,
        .dcim-rack-country {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            font-size: 12px;
            color: #718096;
        }

        .dcim-rack-actions {
            position: absolute;
            top: 12px;
            right: 12px;
            display: flex;
            gap: 4px;
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .dcim-rack-card:hover .dcim-rack-actions {
            opacity: 1;
        }

        /* Inventory Tabs */
        .dcim-inventory-tabs {
            margin-bottom: 24px;
        }

        .dcim-tabs {
            display: flex;
            gap: 2px;
            background: #f8fafc;
            padding: 4px;
            border-radius: 10px;
            border: 1px solid #e2e8f0;
            width: fit-content;
        }

        .dcim-tab {
            padding: 12px 20px;
            background: transparent;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            font-size: 14px;
            color: #718096;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .dcim-tab-active {
            background: white;
            color: #667eea;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
        }

        .dcim-tab:hover:not(.dcim-tab-active) {
            color: #4a5568;
        }

        /* Inventory Filters */
        .dcim-inventory-filters {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            gap: 16px;
        }

        .dcim-filter-group {
            display: flex;
            gap: 12px;
        }

        .dcim-filter {
            padding: 8px 16px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
            background: white;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .dcim-filter:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        /* Table Styles */
        .dcim-inventory-table {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            border: 1px solid #e2e8f0;
        }

        .dcim-table {
            width: 100%;
            border-collapse: collapse;
        }

        .dcim-table th {
            background: #f8fafc;
            padding: 16px;
            text-align: left;
            font-weight: 600;
            font-size: 12px;
            color: #718096;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border-bottom: 1px solid #e2e8f0;
        }

        .dcim-table td {
            padding: 16px;
            border-bottom: 1px solid #f1f5f9;
            font-size: 14px;
            color: #4a5568;
        }

        .dcim-table-row {
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .dcim-table-row:hover {
            background: #f8fafc;
        }

        .dcim-table-row:last-child td {
            border-bottom: none;
        }

        /* Status Styles */
        .dcim-status {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .dcim-status-success {
            background: #e6fffa;
            color: #00a86b;
        }

        .dcim-status-warning {
            background: #fffbeb;
            color: #d69e2e;
        }

        .dcim-status-danger {
            background: #fed7d7;
            color: #e53e3e;
        }

        .dcim-status-info {
            background: #ebf8ff;
            color: #3182ce;
        }

        .dcim-status i {
            font-size: 8px;
        }

        /* Flag Styles */
        .dcim-flag {
            margin-left: 8px;
            border-radius: 2px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        /* Empty State */
        .dcim-empty-state {
            text-align: center;
            padding: 48px;
            color: #718096;
            font-size: 16px;
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .dcim-locations-layout {
                grid-template-columns: 1fr;
            }
            
            .dcim-locations-sidebar {
                order: 2;
            }
            
            .dcim-locations-content {
                order: 1;
            }
            
            .dcim-stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }
        }

        @media (max-width: 768px) {
            .dcim-modern-container {
                padding: 16px;
            }
            
            .dcim-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 16px;
            }
            
            .dcim-title {
                font-size: 24px;
            }
            
            .dcim-stats-grid {
                grid-template-columns: 1fr;
            }
            
            .dcim-action-grid {
                grid-template-columns: 1fr;
            }
            
            .dcim-inventory-filters {
                flex-direction: column;
                align-items: stretch;
            }
            
            .dcim-filter-group {
                flex-wrap: wrap;
            }
            
            .dcim-tabs {
                flex-wrap: wrap;
            }
            
            .dcim-racks-grid {
                grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            }
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .dcim-fade-in {
            animation: fadeInUp 0.4s ease-out;
        }

        /* Loading States */
        .dcim-loading {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 48px;
            color: #718096;
        }

        .dcim-loading i {
            margin-right: 12px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* Error and Setup Pages */
        .dcim-setup-page,
        .dcim-error-page {
            max-width: 600px;
            margin: 0 auto;
            padding: 24px;
        }

        .dcim-setup-page .alert,
        .dcim-error-page .alert {
            border-radius: 12px;
            border: none;
            padding: 24px;
        }

        .dcim-setup-page .alert-warning {
            background: #fffbeb;
            color: #975a16;
        }

        .dcim-error-page .alert-danger {
            background: #fed7d7;
            color: #9b2c2c;
        }

        /* Bulk Add Modal Styles */
        .modal-xl {
            max-width: 1200px;
        }

        .dcim-bulk-add-header {
            padding: 20px 0;
            border-bottom: 1px solid #e2e8f0;
            margin-bottom: 20px;
        }

        .dcim-bulk-actions {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .dcim-bulk-table {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
        }

        .dcim-bulk-table table {
            margin-bottom: 0;
        }

        .dcim-bulk-table th {
            background: #f8fafc;
            border-bottom: 2px solid #e2e8f0;
            font-weight: 600;
            font-size: 12px;
            color: #718096;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .dcim-bulk-table td {
            padding: 12px 8px;
            border-bottom: 1px solid #f1f5f9;
            vertical-align: middle;
        }

        .dcim-bulk-table input,
        .dcim-bulk-table select {
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 8px 12px;
            font-size: 13px;
            width: 100%;
        }

        .dcim-bulk-table input:focus,
        .dcim-bulk-table select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
        }

        .dcim-bulk-summary {
            padding: 16px;
            background: #f8fafc;
            border-radius: 8px;
            margin-top: 20px;
            text-align: center;
            font-weight: 500;
            color: #4a5568;
        }

        .dcim-btn-outline {
            background: transparent;
            border: 1px solid #e2e8f0;
            color: #4a5568;
        }

        .dcim-btn-outline:hover {
            background: #f8fafc;
            border-color: #cbd5e0;
        }

        .dcim-btn-secondary {
            background: #718096;
            color: white;
        }

        .dcim-btn-secondary:hover {
            background: #4a5568;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(113, 128, 150, 0.3);
        }

        /* Tab Styles */
        .nav-tabs {
            border-bottom: 2px solid #e2e8f0;
            margin-bottom: 20px;
        }

        .nav-tabs .nav-link {
            border: none;
            border-radius: 8px 8px 0 0;
            color: #718096;
            font-weight: 500;
            padding: 12px 20px;
            margin-right: 4px;
            transition: all 0.2s ease;
        }

        .nav-tabs .nav-link:hover {
            color: #4a5568;
            background: #f8fafc;
            border-color: transparent;
        }

        .nav-tabs .nav-link.active {
            color: #667eea;
            background: white;
            border-color: #e2e8f0 #e2e8f0 white;
            border-bottom: 2px solid white;
            margin-bottom: -2px;
        }

        .nav-tabs .nav-link i {
            margin-right: 8px;
        }

        .tab-content {
            min-height: 300px;
        }

        /* Input Group Styles */
        .input-group-append .btn {
            border-left: none;
            border-radius: 0 6px 6px 0;
        }

        .input-group .form-control {
            border-radius: 6px 0 0 6px;
        }

        .input-group .form-control:focus {
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
            z-index: 3;
        }

        /* Password Toggle */
        .password-toggle {
            cursor: pointer;
            user-select: none;
        }

        /* Row Actions */
        .dcim-bulk-table .btn-sm {
            padding: 4px 8px;
            font-size: 12px;
            border-radius: 4px;
        }

        .btn-outline-primary {
            border-color: #667eea;
            color: #667eea;
        }

        .btn-outline-primary:hover {
            background: #667eea;
            color: white;
        }

        .btn-outline-danger {
            border-color: #e53e3e;
            color: #e53e3e;
        }

        .btn-outline-danger:hover {
            background: #e53e3e;
            color: white;
        }

        .btn-outline-secondary {
            border-color: #718096;
            color: #718096;
        }

        .btn-outline-secondary:hover {
            background: #718096;
            color: white;
        }

        /* Modal Specific Styles */
        .modal-header {
            border-bottom: 1px solid #e2e8f0;
            padding: 20px 24px;
        }

        .modal-title {
            font-size: 18px;
            font-weight: 600;
            color: #1a202c;
        }

        .modal-title i {
            color: #667eea;
            margin-right: 8px;
        }

        .modal-body {
            padding: 24px;
        }

        .modal-footer {
            border-top: 1px solid #e2e8f0;
            padding: 16px 24px;
        }

        .modal-footer .btn {
            margin-left: 8px;
        }

        /* Responsive Modal */
        @media (max-width: 768px) {
            .modal-xl {
                max-width: 95%;
                margin: 10px auto;
            }
            
            .dcim-bulk-actions {
                flex-direction: column;
                align-items: stretch;
            }
            
            .dcim-bulk-actions .dcim-btn {
                margin-bottom: 8px;
            }
            
            .dcim-bulk-table {
                max-height: 300px;
            }
            
            .nav-tabs .nav-link {
                padding: 8px 12px;
                font-size: 12px;
            }
        }

        /* Animation for dynamic rows */
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .dcim-bulk-table tbody tr {
            animation: slideIn 0.3s ease-out;
        }

        /* Warning Messages */
        .text-warning {
            color: #d69e2e !important;
            font-size: 12px;
            margin-top: 4px;
        }

        /* Hardware Models Management */
        .dcim-hardware-tabs {
            margin-bottom: 32px;
        }

        .dcim-section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e2e8f0;
        }

        .dcim-section-header h2 {
            font-size: 20px;
            font-weight: 600;
            color: #1a202c;
            margin: 0;
        }

        .dcim-hardware-table {
            background: white;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
            overflow: hidden;
        }

        .dcim-tab-content {
            display: none;
        }

        .dcim-tab-content.dcim-tab-active {
            display: block;
        }

        /* Select with Add Button */
        .dcim-select-with-add {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .dcim-select-with-add select {
            flex: 1;
        }

        .dcim-select-with-add .btn {
            padding: 8px 12px;
            font-size: 12px;
            border-radius: 4px;
            white-space: nowrap;
        }

        .dcim-select-with-add .btn-outline-primary {
            border-color: #667eea;
            color: #667eea;
        }

        .dcim-select-with-add .btn-outline-primary:hover {
            background: #667eea;
            color: white;
        }

        /* Modal Enhancements */
        .modal-content {
            border-radius: 12px;
            border: none;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
        }

        .modal-header {
            background: #f8fafc;
            border-bottom: 1px solid #e2e8f0;
            border-radius: 12px 12px 0 0;
        }

        .modal-title {
            font-size: 18px;
            font-weight: 600;
            color: #1a202c;
        }

        .modal-body .form-group {
            margin-bottom: 20px;
        }

        .modal-body label {
            font-weight: 500;
            color: #4a5568;
            margin-bottom: 8px;
        }

        .modal-body .form-control {
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 10px 12px;
            font-size: 14px;
        }

        .modal-body .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .modal-footer {
            border-top: 1px solid #e2e8f0;
            padding: 16px 24px;
        }

        .modal-footer .btn {
            padding: 8px 20px;
            font-size: 14px;
            border-radius: 6px;
        }

        /* Responsive Adjustments */
        @media (max-width: 768px) {
            .dcim-select-with-add {
                flex-direction: column;
                align-items: stretch;
            }
            
            .dcim-select-with-add .btn {
                margin-top: 8px;
            }
            
            .dcim-section-header {
                flex-direction: column;
                align-items: stretch;
                gap: 16px;
            }
        }
        .dcim-modern-container ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        .dcim-modern-container ::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 4px;
        }

        .dcim-modern-container ::-webkit-scrollbar-thumb {
            background: #cbd5e0;
            border-radius: 4px;
        }

        .dcim-modern-container ::-webkit-scrollbar-thumb:hover {
            background: #a0aec0;
        }

        .dcim-bulk-table ::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }

        .dcim-bulk-table ::-webkit-scrollbar-track {
            background: #f8fafc;
        }

        .dcim-bulk-table ::-webkit-scrollbar-thumb {
            background: #cbd5e0;
            border-radius: 3px;
        }
        </style>';
    }
});

/**
 * Hook to add modern JavaScript to admin area footer
 */
add_hook('AdminAreaFooterOutput', 1, function($vars) {
    $moduleName = $_GET['module'] ?? '';
    
    // Only add JavaScript when DCIM module is active
    if ($moduleName == 'dcim') {
        return '
        <script>
        // DCIM Modern JavaScript
        $(document).ready(function() {
            // Initialize modern interface
            initDCIMInterface();
            
            // Add fade-in animations to all cards
            $(".dcim-stat-card, .dcim-action-card, .dcim-rack-card").addClass("dcim-fade-in");
            
            // Handle tab switching
            $(".dcim-tab").on("click", function() {
                $(".dcim-tab").removeClass("dcim-tab-active");
                $(this).addClass("dcim-tab-active");
            });
            
            // Handle search functionality
            $("#location-search").on("keyup", function() {
                var value = $(this).val().toLowerCase();
                $(".dcim-country-item").filter(function() {
                    $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
                });
            });
            
            $("#inventory-search").on("keyup", function() {
                var value = $(this).val().toLowerCase();
                $(".dcim-table-row").filter(function() {
                    $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
                });
            });
            
            // Handle filter changes
            $(".dcim-filter").on("change", function() {
                // Add filter logic here
                console.log("Filter changed:", $(this).val());
            });
            
            // Add hover effects
            $(".dcim-stat-card, .dcim-action-card, .dcim-rack-card").hover(
                function() {
                    $(this).addClass("dcim-hover");
                },
                function() {
                    $(this).removeClass("dcim-hover");
                }
            );
            
            // Handle confirmation dialogs
            $(".dcim-btn-danger").on("click", function(e) {
                if (!confirm("Are you sure you want to delete this item? This action cannot be undone.")) {
                    e.preventDefault();
                    return false;
                }
            });
            
            // Add tooltips
            $("[title]").tooltip({
                placement: "top",
                trigger: "hover"
            });
            
            // Auto-refresh functionality
            if (window.location.href.indexOf("action=dashboard") !== -1 || window.location.href.indexOf("action=") === -1) {
                setInterval(function() {
                    if (typeof loadDashboardStats === "function") {
                        loadDashboardStats();
                    }
                }, 30000);
            }
        });
        
        function initDCIMInterface() {
            // Initialize any complex UI components
            console.log("DCIM Modern Interface initialized");
        }
        
        function showServerDetails(serverId) {
            // Show server details modal
            alert("Server details for ID: " + serverId + " (Modal implementation coming soon)");
        }
        
        // Global DCIM utilities
        window.DCIM = {
            // Show notifications
            showNotification: function(message, type = "info") {
                var alertClass = "alert-" + type;
                var notification = $("<div class=\"alert " + alertClass + " alert-dismissible fade show\" role=\"alert\">" +
                    "<strong>" + message + "</strong>" +
                    "<button type=\"button\" class=\"close\" data-dismiss=\"alert\">" +
                    "<span>&times;</span>" +
                    "</button>" +
                    "</div>");
                
                $("body").prepend(notification);
                
                setTimeout(function() {
                    notification.fadeOut();
                }, 5000);
            },
            
            // Loading state
            showLoading: function(element) {
                $(element).html("<div class=\"dcim-loading\"><i class=\"fas fa-spinner fa-spin\"></i> Loading...</div>");
            },
            
            // Format numbers
            formatNumber: function(num) {
                return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
            },
            
            // Validate IP address
            validateIP: function(ip) {
                var ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
                return ipRegex.test(ip);
            },
            
            // Copy to clipboard
            copyToClipboard: function(text) {
                navigator.clipboard.writeText(text).then(function() {
                    DCIM.showNotification("Copied to clipboard", "success");
                });
            },
            
            // Toggle element visibility
            toggleElement: function(selector) {
                $(selector).toggle();
            },
            
            // Smooth scroll to element
            scrollTo: function(selector) {
                $("html, body").animate({
                    scrollTop: $(selector).offset().top - 20
                }, 500);
            }
        };
        
        // Handle responsive navigation
        function handleResponsiveNav() {
            if ($(window).width() < 768) {
                $(".dcim-locations-sidebar").addClass("mobile-sidebar");
            } else {
                $(".dcim-locations-sidebar").removeClass("mobile-sidebar");
            }
        }
        
        $(window).resize(handleResponsiveNav);
        handleResponsiveNav();
        
        // Keyboard shortcuts
        $(document).keydown(function(e) {
            // Ctrl/Cmd + K for search
            if ((e.ctrlKey || e.metaKey) && e.keyCode === 75) {
                e.preventDefault();
                $("#location-search, #inventory-search").focus();
            }
            
            // Escape to close modals
            if (e.keyCode === 27) {
                $(".modal").modal("hide");
            }
        });
        
        // Performance optimization
        $(window).on("load", function() {
            // Lazy load images
            $("img[data-src]").each(function() {
                $(this).attr("src", $(this).data("src"));
            });
            
            // Initialize charts if needed
            if (typeof Chart !== "undefined") {
                // Initialize charts here
            }
        });
        </script>';
    }
});

/**
 * Hook to add DCIM-specific admin page customizations
 */
add_hook('AdminAreaPage', 1, function($vars) {
    if (isset($vars['filename']) && $vars['filename'] == 'addonmodules.php') {
        $moduleName = $_GET['module'] ?? '';
        
        if ($moduleName == 'dcim') {
            return [
                'pagetitle' => 'DCIM - Data Center Infrastructure Management',
                'breadcrumb' => [
                    'index.php' => 'Dashboard',
                    'addonmodules.php?module=dcim' => 'DCIM'
                ]
            ];
        }
    }
});