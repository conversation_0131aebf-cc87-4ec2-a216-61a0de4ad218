<?php
/**
 * DCIM Module Language File - English
 */

$_ADDONLANG['dcim'] = [
    // General
    'module_name' => 'DCIM - Data Center Infrastructure Management',
    'module_description' => 'Comprehensive data center infrastructure management',
    'dashboard' => 'Dashboard',
    'overview' => 'Overview',
    'statistics' => 'Statistics',
    'recent_activity' => 'Recent Activity',
    'quick_actions' => 'Quick Actions',
    'system_status' => 'System Status',
    
    // Navigation
    'locations' => 'Locations',
    'racks' => 'Racks',
    'servers' => 'Servers',
    'switches' => 'Switches',
    'ip_management' => 'IP Management',
    'reports' => 'Reports',
    'settings' => 'Settings',
    'permissions' => 'Permissions',
    
    // Common Actions
    'add' => 'Add',
    'edit' => 'Edit',
    'delete' => 'Delete',
    'view' => 'View',
    'save' => 'Save',
    'cancel' => 'Cancel',
    'create' => 'Create',
    'update' => 'Update',
    'search' => 'Search',
    'filter' => 'Filter',
    'reset' => 'Reset',
    'back' => 'Back',
    'next' => 'Next',
    'previous' => 'Previous',
    'confirm' => 'Confirm',
    'yes' => 'Yes',
    'no' => 'No',
    
    // Status
    'active' => 'Active',
    'inactive' => 'Inactive',
    'maintenance' => 'Maintenance',
    'reserved' => 'Reserved',
    'provisioning' => 'Provisioning',
    'decommissioned' => 'Decommissioned',
    'failed' => 'Failed',
    'available' => 'Available',
    'assigned' => 'Assigned',
    'blacklisted' => 'Blacklisted',
    
    // Location Management
    'location_management' => 'Location Management',
    'manage_locations' => 'Manage Locations',
    'add_location' => 'Add Location',
    'edit_location' => 'Edit Location',
    'delete_location' => 'Delete Location',
    'view_location' => 'View Location',
    'location_details' => 'Location Details',
    'location_created' => 'Location created successfully',
    'location_updated' => 'Location updated successfully',
    'location_deleted' => 'Location deleted successfully',
    'location_not_found' => 'Location not found',
    'location_name' => 'Location Name',
    'location_type' => 'Location Type',
    'parent_location' => 'Parent Location',
    'no_parent' => 'No Parent',
    'location_address' => 'Address',
    'location_city' => 'City',
    'location_state' => 'State/Province',
    'location_country' => 'Country',
    'location_postal_code' => 'Postal Code',
    'contact_information' => 'Contact Information',
    'contact_name' => 'Contact Name',
    'contact_email' => 'Contact Email',
    'contact_phone' => 'Contact Phone',
    'capacity_information' => 'Capacity Information',
    'total_u_capacity' => 'Total U Capacity',
    'power_capacity_watts' => 'Power Capacity (Watts)',
    'cooling_capacity_btu' => 'Cooling Capacity (BTU)',
    'notes' => 'Notes',
    
    // Location Types
    'region' => 'Region',
    'facility' => 'Facility',
    'floor' => 'Floor',
    'room' => 'Room',
    
    // Rack Management
    'rack_management' => 'Rack Management',
    'manage_racks' => 'Manage Racks',
    'add_rack' => 'Add Rack',
    'edit_rack' => 'Edit Rack',
    'delete_rack' => 'Delete Rack',
    'view_rack' => 'View Rack',
    'rack_details' => 'Rack Details',
    'rack_created' => 'Rack created successfully',
    'rack_updated' => 'Rack updated successfully',
    'rack_deleted' => 'Rack deleted successfully',
    'rack_not_found' => 'Rack not found',
    'rack_name' => 'Rack Name',
    'rack_description' => 'Description',
    'rack_height' => 'Height (U)',
    'rack_power_capacity' => 'Power Capacity (Watts)',
    'rack_cooling_capacity' => 'Cooling Capacity (BTU)',
    'rack_weight_limit' => 'Weight Limit (kg)',
    'rack_position' => 'Position',
    'rack_position_x' => 'Position X',
    'rack_position_y' => 'Position Y',
    'rack_occupancy' => 'Rack Occupancy',
    'available_space' => 'Available Space',
    'used_space' => 'Used Space',
    
    // Server Management
    'server_management' => 'Server Management',
    'manage_servers' => 'Manage Servers',
    'add_server' => 'Add Server',
    'edit_server' => 'Edit Server',
    'delete_server' => 'Delete Server',
    'view_server' => 'View Server',
    'server_details' => 'Server Details',
    'server_created' => 'Server created successfully',
    'server_updated' => 'Server updated successfully',
    'server_deleted' => 'Server deleted successfully',
    'server_not_found' => 'Server not found',
    'hostname' => 'Hostname',
    'asset_tag' => 'Asset Tag',
    'serial_number' => 'Serial Number',
    'manufacturer' => 'Manufacturer',
    'model' => 'Model',
    'u_position' => 'U Position',
    'u_size' => 'U Size',
    'cpu_specs' => 'CPU Specifications',
    'ram_gb' => 'RAM (GB)',
    'storage_specs' => 'Storage Specifications',
    'power_consumption' => 'Power Consumption (Watts)',
    'management_ip' => 'Management IP',
    'primary_ip' => 'Primary IP',
    'operating_system' => 'Operating System',
    'hardware_specs' => 'Hardware Specifications',
    'network_info' => 'Network Information',
    
    // Switch Management
    'switch_management' => 'Switch Management',
    'manage_switches' => 'Manage Switches',
    'add_switch' => 'Add Switch',
    'edit_switch' => 'Edit Switch',
    'delete_switch' => 'Delete Switch',
    'view_switch' => 'View Switch',
    'switch_details' => 'Switch Details',
    'switch_created' => 'Switch created successfully',
    'switch_updated' => 'Switch updated successfully',
    'switch_deleted' => 'Switch deleted successfully',
    'switch_not_found' => 'Switch not found',
    'switch_name' => 'Switch Name',
    'port_count' => 'Port Count',
    'snmp_community' => 'SNMP Community',
    'switch_stack' => 'Switch Stack',
    'port_management' => 'Port Management',
    'connectivity_map' => 'Connectivity Map',
    
    // IP Management (IPAM)
    'ip_management' => 'IP Address Management',
    'ipam' => 'IPAM',
    'manage_ip_addresses' => 'Manage IP Addresses',
    'manage_subnets' => 'Manage Subnets',
    'add_subnet' => 'Add Subnet',
    'edit_subnet' => 'Edit Subnet',
    'delete_subnet' => 'Delete Subnet',
    'view_subnet' => 'View Subnet',
    'subnet_details' => 'Subnet Details',
    'subnet_created' => 'Subnet created successfully',
    'subnet_updated' => 'Subnet updated successfully',
    'subnet_deleted' => 'Subnet deleted successfully',
    'subnet_not_found' => 'Subnet not found',
    'subnet_name' => 'Subnet Name',
    'network_address' => 'Network Address',
    'prefix_length' => 'Prefix Length',
    'ip_version' => 'IP Version',
    'gateway' => 'Gateway',
    'vlan_id' => 'VLAN ID',
    'subnet_description' => 'Description',
    'ip_address' => 'IP Address',
    'ip_addresses' => 'IP Addresses',
    'assigned_to' => 'Assigned To',
    'assignment_type' => 'Assignment Type',
    'ip_utilization' => 'IP Utilization',
    'available_ips' => 'Available IPs',
    'assigned_ips' => 'Assigned IPs',
    'reserved_ips' => 'Reserved IPs',
    'blacklisted_ips' => 'Blacklisted IPs',
    
    // WHMCS Integration
    'whmcs_integration' => 'WHMCS Integration',
    'linked_service' => 'Linked Service',
    'linked_client' => 'Linked Client',
    'link_to_service' => 'Link to Service',
    'link_to_client' => 'Link to Client',
    'unlink' => 'Unlink',
    'auto_assignment' => 'Auto Assignment',
    'manual_assignment' => 'Manual Assignment',
    
    // Reports
    'reports' => 'Reports',
    'capacity_report' => 'Capacity Report',
    'utilization_report' => 'Utilization Report',
    'inventory_report' => 'Inventory Report',
    'audit_report' => 'Audit Report',
    'export_report' => 'Export Report',
    'generate_report' => 'Generate Report',
    
    // Permissions
    'permissions' => 'Permissions',
    'access_control' => 'Access Control',
    'role_permissions' => 'Role Permissions',
    'admin_role' => 'Admin Role',
    'permission_granted' => 'Permission Granted',
    'permission_denied' => 'Permission Denied',
    'access_denied' => 'Access denied',
    'insufficient_permissions' => 'You do not have sufficient permissions to perform this action',
    
    // Validation Messages
    'field_required' => 'This field is required',
    'invalid_email' => 'Invalid email address',
    'invalid_ip' => 'Invalid IP address',
    'invalid_hostname' => 'Invalid hostname',
    'invalid_cidr' => 'Invalid CIDR notation',
    'invalid_vlan_id' => 'VLAN ID must be between 1 and 4094',
    'name_must_be_unique' => 'Name must be unique',
    'circular_reference' => 'This would create a circular reference',
    'cannot_delete_with_children' => 'Cannot delete item with associated child items',
    
    // Error Messages
    'error_occurred' => 'An error occurred',
    'operation_failed' => 'Operation failed',
    'database_error' => 'Database error',
    'validation_failed' => 'Validation failed',
    'not_found' => 'Item not found',
    'already_exists' => 'Item already exists',
    'cannot_delete' => 'Cannot delete item',
    'access_denied_message' => 'You do not have permission to access this resource',
    
    // Success Messages
    'operation_successful' => 'Operation completed successfully',
    'changes_saved' => 'Changes saved successfully',
    'item_created' => 'Item created successfully',
    'item_updated' => 'Item updated successfully',
    'item_deleted' => 'Item deleted successfully',
    
    // Pagination
    'showing_results' => 'Showing {start} to {end} of {total} results',
    'no_results' => 'No results found',
    'page' => 'Page',
    'of' => 'of',
    'per_page' => 'per page',
    
    // API
    'api_access' => 'API Access',
    'api_key' => 'API Key',
    'api_documentation' => 'API Documentation',
    'rate_limit' => 'Rate Limit',
    'requests_per_hour' => 'Requests per Hour',
    
    // Audit Log
    'audit_log' => 'Audit Log',
    'audit_trail' => 'Audit Trail',
    'action_performed' => 'Action Performed',
    'performed_by' => 'Performed By',
    'timestamp' => 'Timestamp',
    'old_values' => 'Old Values',
    'new_values' => 'New Values',
    'ip_address' => 'IP Address',
    'user_agent' => 'User Agent',
    
    // Configuration
    'configuration' => 'Configuration',
    'module_settings' => 'Module Settings',
    'enable_audit_logging' => 'Enable Audit Logging',
    'default_rack_height' => 'Default Rack Height',
    'enable_ip_auto_assignment' => 'Enable IP Auto Assignment',
    'enable_api_access' => 'Enable API Access',
    'api_rate_limit' => 'API Rate Limit',
    'enable_whmcs_integration' => 'Enable WHMCS Integration',
    'notification_email' => 'Notification Email',
    'enable_visual_rack_view' => 'Enable Visual Rack View',
    'backup_retention_days' => 'Backup Retention Days',
];
